/* eslint-disable */
import React, { useState, useEffect, useRef, useMemo } from "react";
import { Link, useParams, useHistory } from "react-router-dom";
import Select from "react-select";
import { useTranslation } from "react-i18next";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import {automationHeaderLinks} from "helper/AutomationHelper/AutomationMenuLinks";

import BreadCrumb from "components/Common/BreadCrumb/BreadCrumb";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import {
  postData,
  getData,
  putData,
} from "utils/Gateway";

import typeOptions from "data/Automation/Type.json";
import actionOptions from "data/Automation/Actions.json";


const AutomationSave = () => {
  const editor = useRef(null);

  const { t } = useTranslation(); //for translation
  // -------- breadcrumb text --------
  const breadCrumbText = [
    { title: t("Automation"), link: "/admin/automation/list" },
    { title: t("Save") },
  ];

  const commonHeaderObject = automationHeaderLinks(t);

  const params = useParams();
  const history = useHistory();
  const moduleAccess = localStorage.getItem("moduleaccess");

  const loadingCircle = [1, 2, 3, 4, 5, 6];

  const token = localStorage.getItem("token");

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));


  // states
  const [tagOptions, setTagOptions] = useState([]); // State to store filtered tag options
  const [usersInput, setUsersInput] = useState(""); // State for followers input
  const [assignedUsers, setAssignedUsers] = useState([]);
  const [messageTemplates, setMessageTemplates] = useState([]);
  const [taskTemplates, setTaskTemplates] = useState([]);
  const [moduleOptions, setModuleOptions] = useState([]);

  const [isSaving, setIsSaving] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");
  const [showAlert, setShowAlert] = useState(false);

  const validationHandler = () => {
    let isValid = true;
    
    return isValid;
  };


  const [trigger, setTrigger] = useState({
    module: null,
    type: null,
    value: "",
  });
  const [steps, setSteps] = useState([
    { action: "", duration: 0, parameters: "" },
  ]);

  const [errors, setErrors] = useState({
    trigger: { module: false, type: false, value: false },
    steps: false,
  });

  // JSON VALUES FOR SELECT OPTIONS =============================== 

  // Modules to be used in automation 
  const moduleSlugs = [
    "MOD_LEAD",
    "MOD_CONTACT",
    "MOD_USER",
  ];
  const moduleSlugsString = moduleSlugs.join(",");

  // function to get module options
  const getModulesBySlug = async() => {

    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_MODULES_BY_SLUG +
        `?token=${token}&slugs=${moduleSlugsString}`;

      const response = await getData(requestUrl);

      console.log("Modules Response:", response);

      const moduleSelectOptions = [];
      if (response.status) {
        response.data.forEach((module) => {
          moduleSelectOptions.push({
            value: module._id,
            label: module.name,
          });
        });
        setModuleOptions(moduleSelectOptions);
      }


    } catch (error) {
      console.log(error.message);
    }
  }


  // steps status options
  const statusOptions = [
    { value: "1", label: "Enable" },
    { value: "0", label: "Disable" },
  ];

  // functions to get data ================
  //function for get all tags
  const getAllTags = async () => {
      try {
      let requestUrl =
          url.API_BASE_URL +
          url.API_GET_ALL_TAGS +
          `?token=${token}`;

      const response = await getData(requestUrl);

      if (response.status) {
          setTagOptions(response.data); // Initialize tag options with all tags
      }
      } catch (error) {
      setAlertMessage(error.message);
      setMessageType("error");
      setShowAlert(true);
      }
  };

  //function for get users list
  const getUsersList = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USERLIST_OPTIONS +
        `?token=${token}&userstringinput=${usersInput}`;

      const response = await getData(requestUrl);

      if (response.status) {
          if (response.data.length > 0) {
            setAssignedUsers(response.data);
          }
      }
    } catch (error) {
      console.log(error);
    }
  };

  //function for get followers input
  const usersInputHandler = (val) => {
    setUsersInput(val);
  };


  //function for get all mail templates
  const getAllMailTemplates = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_MAIL_TEMPLATES +
        `?token=${token}`;

      const response = await getData(requestUrl);

      console.log('Mail Templates', response);

      if (response.status) {
        const apiData = response.data.map((template) => ({
          ...template,
          label: template.name,
          value: template._id,
        }));
        setMessageTemplates(apiData);
      }
    } catch (error) {
      console.log(error.message);
    }
  };



  const handleTriggerChange = (field, value) => {
    setTrigger((prev) => ({ ...prev, [field]: value }));
  };

  const handleStepChange = (index, field, value) => {
    const updatedSteps = [...steps];
    updatedSteps[index][field] = value;

    console.log("Updated Steps:", updatedSteps);

    setSteps(updatedSteps);
  };

  // Function to map selected tag value from tagOptions
  const getSelectedTag = (value) => {
    return tagOptions.find((tag) => tag.value === value) || null;
  };

  const handleWaitDurationChange = (index, field, value) => {
    const updatedSteps = [...steps];
    if (field === "days") {
      const minutes = value * 24 * 60; // Convert days to minutes
      updatedSteps[index].duration = minutes;
    } else if (field === "minutes") {
      updatedSteps[index].duration = value;
    }
    setSteps(updatedSteps);
  };

  //function for automation details
  const getAutomationDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_AUTOMATION_CONFIG_DETAILS +
        `/${params.id}?token=${token}`;

      console.log('Automation Details Request URL:', requestUrl);

      const response = await getData(requestUrl);

      console.log('Automation Details Response:', response);

      if (response.status) {
        const automationData = response.data;
        
        // Set trigger data
        setTrigger({
          module: automationData.trigger.module? automationData.trigger.module._id : null,
          type: automationData.trigger.type,
          value: automationData.trigger.value || "",
        });

        // Set steps data
        const updatedSteps = automationData.steps.map((step) => ({
          action: step.action,
          duration: step.duration || 0,
          parameters: step.parameters || "",
        }));

        setSteps(updatedSteps);
      }

    } catch (error) {
      console.log(error.message);
    }
  };

  //function for save automation
  const saveAutomationHandler = async () => {
    const newErrors = {
      trigger: {
        module: !trigger.module,
        type: !trigger.type,
        value: !trigger.value,
      },
      steps: steps.length === 0 || steps.every((step) => !step.action),
    };

    setErrors(newErrors);

    if (Object.values(newErrors.trigger).some((error) => error) || newErrors.steps) {
      return;
    }

    if (validationHandler()) {
      try {
        setIsSaving(true);

        console.log("Trigger Data:", trigger);
        console.log("Steps Data:", steps);

        const data = {
          trigger,
          steps,
        };

        let response = {};

        if (params.id) {
          let requestUrl =
            url.API_BASE_URL +
            url.API_AUTOMATION_CONFIG +
            `/${params.id}?token=${token}`;

          response = await putData(requestUrl, data);
        } else {
          let requestUrl =
            url.API_BASE_URL + url.API_AUTOMATION_CONFIG + `?token=${token}`;

          response = await postData(requestUrl, data);

          if (response.status) {
            setAlertMessage("Automation saved successfully.");
            setMessageType("success");
            setShowAlert(true);
          } else {
            setAlertMessage("Failed to save automation.");
            setMessageType("error");
            setShowAlert(true);
          }

        }

        setIsSaving(false);

        console.log(response);
       
      } catch (error) {
        console.log(error.message);
      }
    }
  };

  // Function to add a new step
  const addStep = () => {
    setSteps((prevSteps) => [
      ...prevSteps,
      {
      
        action: "",
        duration: 0,
        parameters: "",
      },
    ]);
  };

  // Function to remove a step
  const removeStep = (index) => {
    setSteps((prevSteps) => prevSteps.filter((_, i) => i !== index));
  };

  // Reorder function for drag-and-drop
  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  // Handle drag-and-drop end
  const onDragEnd = (result) => {
    if (!result.destination) return;

    const reorderedSteps = reorder(steps, result.source.index, result.destination.index);
    setSteps(reorderedSteps);
  };

    useEffect(() => {
        if (params.id) {
            getAutomationDetails();
        }
    }, [params.id]);

    // Fetch initial data
    useEffect(() => {
        getAllTags();
        getAllMailTemplates();
        getModulesBySlug();
    }, []);

    // users input state
    useEffect(() => {
      if (usersInput.length > 0) {
        getUsersList();
      }
    }, [usersInput]);



  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN"
  ) {
    return (
      <div id="content_wrapper">
        <section className="event-details-wrapper bg-white pb-5">

          <TabsHeader
              commonHeaderObject={commonHeaderObject}
              activeOption={t("Automation Config")}
            />

          <div className="container-fluid px-lg-5 pt-4 pt-md-0">
            {/* -------- bread crumb ---------- */}
            <BreadCrumb breadCrumbText={breadCrumbText} bottom={true} />

            {/* --- Trigger Section ----- */}
            <div className="p-3 p-lg-4 border border-gray-300 rounded-10 shadow-sm mb-4">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form-group mb-3">
                  <label className="d-block fs-sm fw-semibold mb-2">{t("Trigger")}</label>
                  <div className="row">
                    <div className="col-lg-4">
                      <label className="d-block fs-sm fw-semibold mb-2">{t("Module")}</label>
                      <Select
                        options={moduleOptions}
                        value={moduleOptions.length > 0 && trigger.module && moduleOptions.find(opt => opt.value === trigger.module)} 
                        onChange={(val) => handleTriggerChange("module", val.value)}
                        placeholder={t("Select Module")}
                      />
                      {errors.trigger.module && (
                        <small className="text-danger">{t("Module is required.")}</small>
                      )}
                    </div>
                    <div className="col-lg-4">
                      <label className="d-block fs-sm fw-semibold mb-2">{t("Type")}</label>
                      <Select
                        options={typeOptions}
                        value={typeOptions.length > 0 && trigger.type && typeOptions.find(opt => opt.value === trigger.type)}
                        onChange={(val) => handleTriggerChange("type", val.value)}
                        placeholder={t("Select Type")}
                      />
                      {errors.trigger.type && (
                        <small className="text-danger">{t("Type is required.")}</small>
                      )}
                    </div>
                    <div className="col-lg-4">
                      <label className="d-block fs-sm fw-semibold mb-2">{t("Value")}</label>
                      
                      <Select
                        options={tagOptions}
                        value={tagOptions.length > 0 && trigger.value && getSelectedTag(trigger.value)}
                        onChange={(val) => handleTriggerChange("value", val.value)}
                        placeholder={t("Search and Select Tag")}
                      />
                      {errors.trigger.value && (
                        <small className="text-danger">{t("Value is required.")}</small>
                      )}
                    </div>
                  </div>
                </div>
              </form>
            </div>

            {/* --- Steps Section ----- */}
            <div className="p-3 p-lg-4 border border-gray-300 rounded-10 shadow-sm">
              <label className="d-block fs-sm fw-semibold mb-2">{t("Steps / Actions")}</label>
              {errors.steps && (
                <small className="text-danger d-block mb-2">{t("At least select 1 step with an action.")}</small>
              )}
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="steps">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} style={{ marginTop: "10px" }}>
                      {steps.map((step, index) => (
                        <Draggable key={index} draggableId={`step-${index}`} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                padding: "16px",
                                marginBottom: "8px",
                                borderRadius: "8px",
                                border: "1px solid #ddd",
                                ...provided.draggableProps.style,
                              }}
                            >
                              <div className="d-flex justify-content-between align-items-center mb-3 bg-light p-1">
                                <div className="d-flex align-items-center gap-2">
                                  <span
                                    {...provided.dragHandleProps}
                                    className="material-symbols-outlined text-primary"
                                    style={{ cursor: "grab", fontSize: "24px" }}
                                  >
                                    drag_indicator
                                  </span>
                                  <span className="fs-sm fw-semibold text-primary">{t(`STEP : ${index + 1}`)}</span>
                                </div>
                                <Link
                                  to="#"
                                  className="text-danger"
                                  onClick={() => removeStep(index)}
                                >
                                  <span className="material-symbols-outlined">delete</span>
                                </Link>
                              </div>
                              <div className="row">
                                <div className="col-lg-3">
                                  <label className="d-block fs-sm fw-semibold mb-2">{t("Action")}</label>
                                  <Select
                                    options={actionOptions}
                                    value={actionOptions.find((opt) => opt.value === step.action)}
                                    onChange={(val) => handleStepChange(index, "action", val.value)}
                                    placeholder={t("Select Action")}
                                  />
                                </div>

                                {/* add remove tag *********************** */}
                                {(step.action === "add_tag" || step.action === "remove_tag") && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Select Tag")}</label>
                                    <Select
                                      options={tagOptions}
                                      value={tagOptions.length > 0 && step.parameters && tagOptions.find(tag => tag.value === step.parameters)}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Search and Select Tag")}
                                    />
                                  </div>
                                )}

                                {/* Wait Action  **************************** */}
                                {step.action === "wait" && (
                                  <>
                                    <div className="col-lg-3">
                                      <label className="d-block fs-sm fw-semibold mb-2">{t("Wait Duration (days) Optional")}</label>
                                      <input
                                        type="number"
                                        className="form-control fs-sm shadow-none"
                                        placeholder={t("Enter Days")}
                                        onChange={(e) => handleWaitDurationChange(index, "days", e.target.value)}
                                      />
                                    </div>
                                    <div className="col-lg-3">
                                      <label className="d-block fs-sm fw-semibold mb-2">{t("Wait Duration (minutes)")}</label>
                                      <input
                                        type="number"
                                        className="form-control fs-sm shadow-none"
                                        value={step.duration}
                                        placeholder={t("Enter Minutes")}
                                        min={1}
                                        onChange={(e) => handleWaitDurationChange(index, "minutes", e.target.value)}
                                      />
                                    </div>
                                  </>
                                )}

                                {/* send mail **************************** */}
                                {step.action === "send_email" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Select Message Template")}</label>
                                    <Select
                                      options={messageTemplates} // Replace with message template options
                                      value={messageTemplates.length > 0 && step.parameters && messageTemplates.find(template => template.value === step.parameters)}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Select Template")}
                                    />
                                  </div>
                                )}

                                

                                {/* Create Task  **************************** */}
                                {step.action === "create_task" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Task Template")}</label>
                                    <Select
                                      options={[]} // Replace with task template options
                                      //value={step.parameters ? taskTemplates.find(template => template.value === step.parameters) : null}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Select Task Template")}
                                    />
                                  </div>
                                )}

                                {/* Send Notification  **************************** */}
                                {step.action === "send_notification" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Notification Mail Template")}</label>
                                    <Select
                                      options={[]} // Replace with notification template options
                                      value={messageTemplates.length > 0 && step.parameters && messageTemplates.find(template => template.value === step.parameters)}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Select Mail Template")}
                                    />
                                  </div>
                                )}

                                {/* Assign User  **************************** */}
                                {step.action === "assign_user" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Select Contact")}</label>
                                    <Select
                                      options={assignedUsers}// Replace with user options
                                      value={assignedUsers.length > 0 && step.parameters && assignedUsers.find(user => user.value === step.parameters)}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Select User")}
                                      onInputChange={(val) => {
                                        usersInputHandler(val);
                                      }}
                                    />
                                  </div>
                                )}

                                {/* Trigger Webhook  **************************** */}
                                {step.action === "trigger_webhook" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Webhook URL")}</label>
                                    <input
                                      type="text"
                                      className="form-control fs-sm shadow-none"
                                      value={step.parameters || ""}
                                      onChange={(e) => handleStepChange(index, "parameters", e.target.value)}
                                      placeholder={t("Enter Webhook URL")}
                                    />
                                  </div>
                                )}

                                {/* Change Status  **************************** */}
                                {step.action === "change_status" && (
                                  <div className="col-lg-3">
                                    <label className="d-block fs-sm fw-semibold mb-2">{t("Select Status")}</label>
                                    <Select
                                      options={statusOptions}
                                      value={statusOptions.find((opt) => opt.value === step.parameters)}
                                      onChange={(val) => handleStepChange(index, "parameters", val.value)}
                                      placeholder={t("Select Enable or Disable")}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
              <button type="button" className="btn btn-sm btn-outline-primary mt-3" onClick={addStep}>
                {t("Add Step")}
              </button>
            </div>

            {/* --- Save Button ----- */}
            <div className="action d-flex flex-wrap gap-3 align-items-center justify-content-end mt-4">
              <button
                type="button"
                className="btn btn-primary"
                style={{ cursor: isSaving ? "not-allowed" : "pointer" }}
                disabled={isSaving ? true : false}
                onClick={saveAutomationHandler}
              >
                {t("Save")}
                {isSaving && (
                  <div className="mx-2 spinner-border spinner-border-sm" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                )}
              </button>
            </div>
          </div>

          
        </section>
      </div>
    );
  } else {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper bg-white pb-5">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-lg text-gray fw-semibold mb-4">
                {t("Sorry....! You don't have privilege to see this content")}
              </p>
            </div>
          </div>
        </section>
      </div>
    );
  }
};

export default AutomationSave;
