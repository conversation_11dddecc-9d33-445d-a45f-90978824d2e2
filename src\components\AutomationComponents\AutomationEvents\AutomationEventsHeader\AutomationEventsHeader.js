/* eslint-disable */
import React from "react";
import { <PERSON> } from "react-router-dom";
import BreadCrumb from "components/Common/BreadCrumb/BreadCrumb";
import { useTranslation } from "react-i18next";

const AutomationEventsHeader = ({ reloadList = () => {} }) => {
  /* ---- bread crumb text ---- */
  const breadcrumbText = [{ title: "Automation Events" }];

  const { t } = useTranslation();

  return (
    <div className="filter-container py-3">
      <div className="row align-items-center">
        <div className="col-md-2 col-lg-2 d-none d-md-block mb-3 mb-md-0">
          {/* --- breadcrumb start --- */}
          <BreadCrumb breadCrumbText={breadcrumbText} top={false} />
          {/* --- breadcrumb end --- */}
        </div>
        <div className="col-md-10 col-lg-10 d-flex justify-content-end gap-1 gap-sm-2">
          <Link
            onClick={reloadList}
            to="#"
            className="btn btn-gray d-flex align-items-center"
          >
            <span className="d-block material-symbols-outlined icon-md">
              refresh
            </span>
          </Link>

          <Link
            to="#"
            className="btn btn-gray d-flex align-items-center gap-1"
            type="button"
            // data-bs-toggle="offcanvas"
            // data-bs-target="#offcanvasUserFilter"
            // aria-controls="offcanvasUserFilter"
          >
            <span className="d-block material-symbols-outlined icon-md">
              tune
            </span>
            <span className="d-block">{t("Filter")}</span>
          </Link>

          <div className="dropdown flex-fill flex-grow-sm-0">
            <button
              className="btn btn-gray text-start w-100 dropdown-toggle"
              type="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
            >
              {t("Action")}
            </button>
            <ul className="dropdown-menu w-100 bg-white fs-sm border-0 rounded-10 shadow-sm">
              
              <li>
                <Link
                  to="#"
                  className="dropdown-item d-flex align-items-center gap-1"
                  // data-bs-toggle="modal"
                  // data-bs-target="#addLabelModal"
                >
                  <span className="d-block material-symbols-outlined icon-sm">
                    label
                  </span>
                  <span className="d-block">{t("Add Label")}</span>
                </Link>
              </li>
    
              <li>
                <Link
                  to="#"
                  className="dropdown-item d-flex align-items-center gap-1"
                >
                  <span className="d-block material-symbols-outlined icon-sm">
                    check_circle
                  </span>
                  <span className="d-block">{t("Enable Selected")}</span>
                </Link>
              </li>

              <li>
                <Link
                  to="#"
                  className="dropdown-item d-flex align-items-center gap-1"
                >
                  <span className="d-block material-symbols-outlined icon-sm">
                    cancel
                  </span>
                  <span className="d-block">{t("Disable Selected")}</span>
                </Link>
              </li>

              
            </ul>
          </div>


        </div>
      </div>
    </div>
  );
};

export default AutomationEventsHeader;
