/* eslint-disable */
import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import Select from "react-select";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

import { assetImages } from "constants/index";

//--- dnd-kit ---------------------
import {
  DndContext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";

import { arrayMove, sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

//--- dnd-kit ends ---------------------

import AlertNotification from "components/Common/AlertNotification/AlertNotification";
import { formatNumber } from "helper/Common/CommonHelper";

import SaveColumnModal from "components/Common/Modal/SaveColumnModal";
import SaveActivityProcessPopup from "components/Common/Popup/ActivityProcessPopup/SaveActivityProcessPopup";
import TaskManagementPopup from "components/Common/Popup/TaskManagementPopup/TaskManagementPopup";
import AddProcessFromTemplateModal from "components/Common/Modal/AddProcessFromTemplateModal";
import MyTaskKanbanHeader from "./Header/MyTaskKanbanHeader";
import ProjectTaskFilter from "components/ProjectManagementComponents/ProjectDetailsComponents/ProjectTaskComponents/Popup/ProjectTaskFilter";
import MyTaskFilterPopup from "components/Common/Popup/MyTaskFilterPopup";

import { getCurrentDateInString } from "helper/getcurrentdatestring";
// import { createContactChatHandler } from "helper/AppUserHelper/AppUserHelper";
import { createTaskChatHandler } from "helper/TaskHelper/TaskHelper";
import BoardColumn from "./KanbanKit/BoardColumn";
import ColumnItem from "./KanbanKit/ColumnItem";
import { customKanbanSelectProcessStyle } from "Config/Config";
import TaskAdvanceFilterPopup from "components/Common/Popup/TaskAdvanceFilterPopup";

// ------------- dnd-kit ----------------------------

const defaultAnnouncements = {
  onDragStart(id) {
    console.log(`Picked up draggable item ${id}.`);
  },
  onDragOver(id, overId) {
    if (overId) {
      console.log(
        `Draggable item ${id} was moved over droppable area ${overId}.`
      );
      return;
    }

    console.log(`Draggable item ${id} is no longer over a droppable area.`);
  },
  onDragEnd(id, overId) {
    if (overId) {
      console.log(
        `Draggable item ${id} was dropped over droppable area ${overId}`
      );
      return;
    }

    console.log(`Draggable item ${id} was dropped.`);
  },
  onDragCancel(id) {
    console.log(`Dragging was cancelled. Draggable item ${id} was dropped.`);
  },
};
// ---------------------

const MyTaskKanbanKitBody = () => {
  const moduleSlug = "MOD_TASK";
  const params = useParams();

  const { t } = useTranslation(); //for translation
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [sectionList, setSectionList] = useState([]);
  const [sectionOptions, setSectionOptions] = useState([]);
  const [memberList, setMemberList] = useState([]);

  const [isProjectModerator, setisProjectModerator] = useState(false);

  const [selectedSectionId, setSelectedSectionId] = useState(null);
  const [selectedSectionValue, setSelectedSectionValue] = useState(null);
  const [selectedTaskModerator, setselectedTaskModerator] = useState(null);

  const [selectedEditTaskId, setSelectedEditTaskId] = useState(null);

  const [title, settitle] = useState("");

  const [showEmptyProcessMessage, setShowEmptyProcessMessage] = useState(false);
  const [processListLoading, setprocessListLoading] = useState(false);
  const [moderatorName, setModeratorName] = useState("");
  const [processList, setProcessList] = useState([]);

  const [processDetailsLoading, setProcessDetailsLoading] = useState(false);
  const [kanbanData, setKanbanData] = useState([]);

  const [taskCount, setTaskCount] = useState(0);
  const [pendingTaskCount, setpendingTaskCount] = useState(0);
  const [inProgressTaskCount, setinProgressTaskCount] = useState(0);
  const [completedTaskCount, setcompletedTaskCount] = useState(0);
  const [delayedTaskCount, setdelayedTaskCount] = useState(0);
  const [followingTaskCount, setfollowingTaskCount] = useState(0);
  const [testingTaskCount, settestingTaskCount] = useState(0);
  const [taskTotalPendingHours, setTaskTotalPendingHours] = useState(0);

  const [totalTaskHours, settotalTaskHours] = useState(0);
  const [totalTaskLogHours, settotalTaskLogHours] = useState(0);

  // save task popup, add componentOrderId, get task or tag details from there
  const [selectedTagId, setSelectedTagId] = useState(null); // component order id
  const [selectedComponentId, setSelectedComponentId] = useState(null); // task id

  //selected process
  const [selectedProcessvalue, setSelectedProcessvalue] = useState(null);
  const [selectedProcessId, setSelectedProcessId] = useState(null);

  const [filterTaskDateRangeType, setFilterTaskDateRangeType] = useState("");
  const [filterTaskFromDate, setfilterTaskFromDate] = useState("");
  const [filterTaskToDate, setfilterTaskToDate] = useState("");

  const [filterTaskCustomIds, setfilterTaskCustomIds] = useState([]);
  const [filterTaskTitle, setfilterTaskTitle] = useState("");
  const [filterTaskProjects, setfilterTaskProjects] = useState([]);
  const [filterTaskAssignedToUsers, setfilterTaskAssignedToUsers] = useState(
    []
  );
  const [filterTaskAssignedByUsers, setfilterTaskAssignedByUsers] = useState(
    []
  );
  const [filterTaskStatus, setfilterTaskStatus] = useState([]);
  const [filterTaskPriorities, setfilterTaskPriorities] = useState([]);
  const [filterTaskLabels, setfilterTaskLabels] = useState([]);

  const [reloadData, setReloadData] = useState(false);
  const [isFilterReset, setIsFilterReset] = useState(false);

  const [collapseColumnTags, setCollapseColumnTags] = useState([]); // collapse columns with these tags

  // KPI's to show in kanban selected process
  const [followersCount, setFollowersCount] = useState(0);
  const [cardItemCount, setCardItemCount] = useState(0);
  const [cardValueCount, setCardValueCount] = useState(0);

  // json filter query
  const [jsonFilterQuery, setJsonFilterQuery] = useState([]);

  //alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  //get all process
  const getAllProcess = async () => {
    try {
      setprocessListLoading(true);
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_ALL_PROCESS +
        `?token=${token}&ismod=${true}&istemplate=${false}&moduleslug=${moduleSlug}`;

      const response = await getData(requestURL);

      setprocessListLoading(false);

      console.log("response user all process----->", response);

      if (response.status) {
        // if (response.data.length === 0) {
        //   setShowEmptyProcessMessage(true);
        // } else {
        //   setShowEmptyProcessMessage(false);
        // }

        const processOptions = response.data.map((item) => ({
          label: `${item.title} ${item.isowner ? "(Owned)" : ""}`,
          value: item._id,
          ownername: item.ownername,
        }));

        // if (response.lastsavedprocessid) {
        //   proecessSelectionHandler(
        //     processOptions.find(
        //       (item) => item.value === response.lastsavedprocessid
        //     )
        //   );
        // } else {
        //   proecessSelectionHandler(processOptions[processOptions.length - 1]);
        // }

        // console.log("processOptions", processOptions);

        setProcessList(processOptions);
      } else {
        setMessageType("error");
        setAlertMessage(response.message);
        setShowAlert(true);
      }
    } catch (error) {
      setMessageType("error");
      setAlertMessage(error.message);
      setShowAlert(true);
    }
  };

  //select process
  const processSelectionHandler = (val) => {
    if (val) {
      setSelectedProcessvalue(val);
      //getProcessDetails(val.value);
      setSelectedProcessId(val.value);
      setModeratorName(val.ownername);
    } else {
      setSelectedProcessvalue(null);
      //getProcessDetails(null);
      setSelectedProcessId(null);
      setModeratorName("");
    }
  };

  // when add or edit board --------
  const addEditBoardHandler = () => {
    console.log("addEditBoardHandler");

    getAllProcess();

    if (selectedProcessId) {
      getProcessTags();
    }
  };

  useEffect(() => {
    getAllProcess();
  }, []);

  //function for close process template modal
  const afterProcessModalClose = () => {
    getAllProcess();
  };

  //function for reset filter
  const resetFilterData = () => {
    setJsonFilterQuery([]);
    setReloadData(true);
  };

  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  useEffect(() => {
    if (reloadData) {
      setIsFilterReset(true);
      getProcessTags(selectedProcessId);
      setReloadData(false);
    }
  }, [reloadData]);

  // ----------------------------------------------------------------------
  // new callbacks for dnd-kit --------------------------------------------
  // ----------------------------------------------------------------------
  const [isDragging, setIsDragging] = useState(false);

  const [addNewBoard, setAddNewBoard] = useState(false); // if true, open the modal to add a new board
  // if false, open modal to edit the selected board
  const [requestBoardUpdate, setRequestBoardUpdate] = useState(false); // if true, update the board data

  // open the modal to edit the selected board
  const editSelectedBoardHandler = () => {
    console.log("editSelectedBoardHandler");
    setAddNewBoard(false);

    setRequestBoardUpdate(true);

    console.log("selectedProcessId", selectedProcessId);

    const offcanvas = new bootstrap.Offcanvas("#activityProcessOffCanvas");
    offcanvas.show();
  };

  // open the modal to add a new board
  const createNewBoardHandler = () => {
    console.log("createNewBoardHandler");
    setAddNewBoard(true);

    const offcanvas = new bootstrap.Offcanvas("#activityProcessOffCanvas");
    offcanvas.show();
  };

  // copy from template
  const copyFromTemplateHandler = () => {
    console.log("copyFromTemplateHandler");
    setAddNewBoard(false);

    const offcanvas = new bootstrap.Offcanvas("#kanbanExample");
    offcanvas.show();
  };

  // dnd-kit ------------------------------------------------------------------

  const [processTags, setProcessTags] = useState([]);

  const [items, setItems] = useState([]);

  const getProcessTags = async () => {
    try {
      setProcessDetailsLoading(true);

      setProcessTags([]);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_PROCESS_TAGS +
        `/${selectedProcessId}?token=${token}`;

      console.log("requestURL getProcessTags", requestURL);

      const response = await getData(requestURL);

      console.log("response getProcessTags----->", response);

      if (response.status) {
        if (response.data.length > 0) {
          const boardColumns = await Promise.all(
            response.data.map(async (item) => {
              // set item.id in processTags
              setProcessTags((prev) => [...prev, item.value]);

              // set json data for each column ---------------------
              let columnItemsURL =
                url.API_BASE_URL +
                url.API_GET_COMPONENTORDER_TASK_BY_TAGS +
                `/${item.value}?token=${token}`;

              if (jsonFilterQuery.length > 0) {
                // Sanitize the jsonFilterQuery
                const sanitizedQuery = jsonFilterQuery.map((filter) => {
                  if (
                    (filter.operator === "includes" ||
                      filter.operator === "excludes") &&
                    Array.isArray(filter.value)
                  ) {
                    return {
                      ...filter,
                      value: filter.value.map((item) => item.value).join(","), // Extract and join values as a comma-separated string
                    };
                  }
                  return filter;
                });

                columnItemsURL += `&filters=${JSON.stringify(sanitizedQuery)}`;
              }

              console.log("columnItemsURL", columnItemsURL);

              const columnItemsResponse = await getData(columnItemsURL);

              console.log("columnItemsResponse", columnItemsResponse);

              // column items with leads data and component order record id
              const columnItems =
                columnItemsResponse.data.length > 0
                  ? columnItemsResponse.data.map((itemData) => {
                      return {
                        id: itemData._id,
                        order: itemData.order,
                        value: itemData.task._id,
                        label: itemData.task?.title ?? "",
                        task: itemData.task,
                        created: itemData.createdAt,
                        updated: itemData.updatedAt,
                      };
                    })
                  : [];

              // column tag data
              return {
                id: item.value, // tag id
                label: item.label, // tag label
                items: columnItems, // leads with the tag
              };
            })
          );

          console.log("boardColumns", boardColumns);

          setItems(boardColumns);

          const totalTasks = boardColumns.reduce((total, curr) => {
            return total + (curr.items ? curr.items.length : 0);
          }, 0);

          setTaskCount(totalTasks);

          //get follwing tasks
          const followingTasks = boardColumns.reduce((total, curr) => {
            const tasks = curr.items || [];

            const uncompletedTasksInProcess = tasks.filter(
              (taskData) =>
                taskData.task.followers &&
                taskData.task.followers.includes(userInfo._id)
            ).length;

            return total + uncompletedTasksInProcess;
          }, 0);

          setfollowingTaskCount(followingTasks);

          //get pending tasks
          const pendingTaskCount = boardColumns.reduce((total, curr) => {
            const tasks = curr.items || [];

            const uncompletedTasksInProcess = tasks.filter(
              (taskData) => taskData.task.completedpercent === "0"
            ).length;

            return total + uncompletedTasksInProcess;
          }, 0);

          setpendingTaskCount(pendingTaskCount);

          //get in progress tasks
          const inProgressTasks = boardColumns.reduce((total, curr) => {
            const tasks = curr.items || [];

            const uncompletedTasksInProcess = tasks.filter(
              (taskData) => taskData.task.completedpercent === "1"
            ).length;

            return total + uncompletedTasksInProcess;
          }, 0);

          setinProgressTaskCount(inProgressTasks);

          //get completed tasks
          const completedTasks = boardColumns.reduce((total, curr) => {
            const tasks = curr.items || [];

            const uncompletedTasksInProcess = tasks.filter(
              (taskData) => taskData.task.completedpercent === "3"
            ).length;

            return total + uncompletedTasksInProcess;
          }, 0);

          setcompletedTaskCount(completedTasks);

          const delayedFilterTasks = boardColumns.reduce((total, curr) => {
            const tasks = curr.items || [];

            const uncompletedTasksInProcess = tasks.filter(
              (taskData) => taskData.task.tasknumberofdaysleft < 0
            ).length;

            return total + uncompletedTasksInProcess;
          }, 0);

          setdelayedTaskCount(delayedFilterTasks);

          //get total planned hours
          const sumOfPlannedHours = boardColumns.reduce((sum, curr) => {
            return (
              sum +
              (curr.items || []).reduce((taskSum, taskData) => {
                return taskSum + (Number(taskData.task.hours) || 0);
              }, 0)
            );
          }, 0);

          settotalTaskHours(sumOfPlannedHours);

          const sumOfPendingHours = boardColumns.reduce((sum, curr) => {
            return (
              sum +
              (curr.items || []).reduce((taskPendingSum, taskData) => {
                return (
                  taskPendingSum + (Number(taskData.task.taskpendinghours) || 0)
                );
              }, 0)
            );
          }, 0);

          setTaskTotalPendingHours(sumOfPendingHours.toFixed(2));

          //get total dedicated hours
          const sumOfDedicatedHours = boardColumns.reduce((sum, curr) => {
            return (
              sum +
              (curr.items || []).reduce((taskSum, taskData) => {
                return taskSum + (Number(taskData.task.loggedhours) || 0);
              }, 0)
            );
          }, 0);

          settotalTaskLogHours(sumOfDedicatedHours.toFixed(2));
        }

        setProcessDetailsLoading(false);
      }
    } catch (error) {
      console.log("error", error.message);
    }
  };

  useEffect(() => {
    if (selectedProcessId) {
      getProcessTags();
    } else {
      setItems([]);
    }
  }, [selectedProcessId]);

  useEffect(() => {
    console.log("jsonFilterQuery updated:", jsonFilterQuery);

    if (jsonFilterQuery && jsonFilterQuery.length > 0) {
      getProcessTags();
    } else {
      // console.log("No filters applied or jsonFilterQuery is empty.");
    }
  }, [jsonFilterQuery]);

  const [activeId, setActiveId] = useState();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // functions to handle the drag and drop and manage the items in frontend -----------------------------------
  // --------------------------------------
  function findContainer(id) {
    return (
      items.find((container) =>
        container.items.some((item) => item.id === id)
      ) || items.find((container) => container.id === id)
    );
  }

  // ------------------------------------------------
  function handleDragStart(event) {
    console.log("onDragStart");

    const { active } = event;
    const { id } = active;

    setActiveId(id);
  }

  // -------------------------------------------------------
  function handleDragOver(event) {
    console.log("onDragOver");

    const { active, over, draggingRect } = event;
    const { id } = active;
    const { id: overId } = over;

    const activeContainer = findContainer(id);
    const overContainer = findContainer(overId);

    if (
      !activeContainer ||
      !overContainer ||
      activeContainer === overContainer
    ) {
      return;
    }

    setItems((prev) => {
      const activeItems = activeContainer.items;
      const overItems = overContainer.items;

      const activeIndex = activeItems.findIndex((item) => item.id === id);
      const overIndex = overItems.findIndex((item) => item.id === overId);

      let newIndex;
      if (overContainer.items.length === 0) {
        newIndex = 0;
      } else {
        const isBelowLastItem =
          over &&
          over.rect &&
          draggingRect &&
          overIndex === overItems.length - 1 &&
          draggingRect.offsetTop > over.rect.offsetTop + over.rect.height;

        const modifier = isBelowLastItem ? 1 : 0;

        newIndex = overIndex >= 0 ? overIndex + modifier : overItems.length + 1;
      }

      return prev.map((container) => {
        if (container.id === activeContainer.id) {
          return {
            ...container,
            items: [...container.items.filter((item) => item.id !== active.id)],
          };
        } else if (container.id === overContainer.id) {
          return {
            ...container,
            items: [
              ...container.items.slice(0, newIndex),
              activeItems[activeIndex],
              ...container.items.slice(newIndex, container.items.length),
            ],
          };
        } else {
          return container;
        }
      });
    });
  }

  // ----------------------------------------------------------
  const handleDragEnd = async (event) => {
    if (!isDragging) {
      console.log("drag end -- not dragging");
      return;
    }

    setIsDragging(false);

    //console.log('drag end', event);

    const { active, over } = event;

    if (!active || !over) {
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    const activeContainer = findContainer(activeId);
    const overContainer = findContainer(overId);

    if (!activeContainer || !overContainer) {
      return;
    }

    const activeContainerId = activeContainer.id;
    const overContainerId = overContainer.id;

    let updatedItemsDetails = overContainer.items;

    if (activeContainerId !== overContainerId) {
      // Dragging between different containers
      console.log("Dragging between different containers");

      setItems((prevItems) =>
        prevItems.map((container) => {
          if (container.id === activeContainerId) {
            return {
              ...container,
              items: container.items.filter((item) => item.id !== activeId),
            };
          } else if (container.id === overContainerId) {
            return {
              ...container,
              items: [
                ...container.items,
                activeContainer.items.find((item) => item.id === activeId),
              ],
            };
          } else {
            return container;
          }
        })
      );

      updatedItemsDetails = overContainer.items;
    } else {
      // Dragging within the same container
      console.log("Dragging within the same container");
      const activeIndex = activeContainer.items.findIndex(
        (item) => item.id === activeId
      );
      const overIndex = activeContainer.items.findIndex(
        (item) => item.id === overId
      );

      if (activeIndex !== overIndex) {
        const updatedItems = arrayMove(
          activeContainer.items,
          activeIndex,
          overIndex
        );

        setItems((prevItems) =>
          prevItems.map((container) =>
            container.id === activeContainerId
              ? { ...container, items: updatedItems }
              : container
          )
        );

        updatedItemsDetails = updatedItems;
      }
    }

    console.log("activeId", activeId);
    console.log("containerId", overContainerId);
    console.log("itemDetails", updatedItemsDetails);

    // updat component order ------------------------------------
    // Prepare the data to send in the API request
    const updatedOrder = {
      tagids: processTags, // The list of tags in the board
      activeItemId: activeId, // The ID of the dragged item
      containerDetails: {
        containerId: overContainerId, // The ID of the container where the item is dropped
        itemIds: updatedItemsDetails.map((item) => item.id), // The list of item IDs in the new container
      },
    };

    try {
      // API request to update the component order
      const requestURL =
        url.API_BASE_URL +
        url.API_POST_UPDATE_COMPONENTORDER_LEAD +
        `?token=${token}`;

      const response = await postData(requestURL, updatedOrder);

      console.log("Update Data -- response", response);

      if (response.status) {
        setMessageType("success");
        setAlertMessage(response.message);
        setShowAlert(true);
      }
    } catch (error) {
      console.error("Error updating order:", error);
    }

    // update component order ends ------------------------------------
  };

  // --------- manage state to trigger dragEng API call ----------------------------------------
  const handleDragMove = async (event) => {
    console.log("drag move");
    setIsDragging(true);
  };

  // functions ends -----------------------------------

  return (
    <div id="content_wrapper">
      <section className="event-details-wrapper bg-white pb-5">
        <div className="container-fluid px-lg-5 pt-4 pt-md-0">
          <MyTaskKanbanHeader reloadList={resetFilterData} />

          <div className="project-tasks-container">
            {/* KANBAN BOARD --- OPTIONS -- CREATE/UPDATE/MAIL ITEMS */}
            <div className="challenges-process-row d-flex align-items-center justify-content-between gap-3 mb-1">
              <div className="left-process-select d-flex align-items-center gap-3">
                <Select
                  styles={customKanbanSelectProcessStyle}
                  isClearable
                  placeholder="Select process"
                  options={processList}
                  value={selectedProcessvalue}
                  onChange={(val) => processSelectionHandler(val)}
                />

                <ul className="d-flex align-items-center gap-2">
                  <li role="presentation">
                    Tasks : <span className="text-primary">{taskCount}</span>
                  </li>

                  <li role="presentation">
                    Pending :{" "}
                    <span className="text-primary">{pendingTaskCount}</span>
                  </li>

                  <li role="presentation">
                    In progress :{" "}
                    <span className="text-primary">{inProgressTaskCount}</span>
                  </li>

                  <li role="presentation">
                    Completed :{" "}
                    <span className="text-primary">{completedTaskCount}</span>
                  </li>

                  <li role="presentation">
                    <Link
                      to="#"
                      className="d-flex align-items-center justify-content-center dropdown-toggle fw-bold"
                      role="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      <span className="material-symbols-outlined icon-fill fw-bold">
                        more_vert
                      </span>
                    </Link>

                    <ul className="dropdown-menu dropdown-menu-end p-3 bg-white border-0 rounded-10 shadow-sm overflow-hidden">
                      <li role="presentation" className="mb-2">
                        Delayed :{" "}
                        <span className="text-primary">{delayedTaskCount}</span>
                      </li>
                      <li role="presentation" className="mb-2">
                        Following :{" "}
                        <span className="text-primary">
                          {followingTaskCount}
                        </span>
                      </li>

                      <li role="presentation" className="mb-2">
                        Testing :{" "}
                        <span className="text-primary">{testingTaskCount}</span>
                      </li>

                      <li className="mb-2" role="presentation">
                        Completed :{" "}
                        <span className="text-primary">
                          {completedTaskCount}
                        </span>
                      </li>

                      <li className="mb-2" role="presentation">
                        Planned Hours :{" "}
                        <span className="text-primary">{totalTaskHours}</span>
                      </li>

                      <li className="mb-2" role="presentation">
                        Pending Hours :{" "}
                        <span className="text-primary">
                          {taskTotalPendingHours}
                        </span>
                      </li>

                      <li className="mb-2" role="presentation">
                        Dedicated :{" "}
                        <span className="text-primary">
                          {totalTaskLogHours}
                        </span>
                      </li>

                      <li className="mb-1" role="presentation">
                        Desviation :{" "}
                        <span className="text-primary">
                          {" "}
                          {(totalTaskHours - totalTaskLogHours).toFixed(2)}
                        </span>
                      </li>
                    </ul>
                  </li>
                </ul>
              </div>

              <div className="right-process-btn d-flex align-items-center gap-2">
                {selectedProcessId && (
                  <Link
                    to="#"
                    className="btn btn-primary d-flex align-items-center gap-1"
                    onClick={() => editSelectedBoardHandler()}
                  >
                    <span className="d-block material-symbols-outlined icon-md">
                      add
                    </span>
                    <span className="d-block">Edit Board</span>
                  </Link>
                )}

                <div className="dropdown flex-fill flex-grow-sm-0">
                  <button
                    className="btn btn-primary text-start w-100 dropdown-toggle"
                    type="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    {t("Add New Board")}
                  </button>
                  <ul className="dropdown-menu w-100 bg-white fs-sm border-0 rounded-10 shadow-sm">
                    <li>
                      <Link
                        className="dropdown-item d-flex align-items-center gap-1"
                        to="#"
                        onClick={() => createNewBoardHandler()}
                      >
                        <span className="d-block material-symbols-outlined icon-sm">
                          add
                        </span>
                        <span className="d-block">
                          {t("Create a new board")}
                        </span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="#kanbanExample"
                        className="dropdown-item d-flex align-items-center gap-1"
                        data-bs-toggle="modal"
                        data-bs-target="#kanbanExample"
                      >
                        <span className="d-block material-symbols-outlined icon-sm">
                          view_kanban
                        </span>
                        <span className="d-block">
                          {t("Copy from template")}
                        </span>
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* KANBAN BOARD -------------  */}

            {selectedProcessId ? (
              processDetailsLoading ? (
                // show placeholder of loading
                <div className="board_outer">
                  <div className="project-tasks-container d-flex overflow-x-auto pb-3 grid-view gap-2">
                    {[1, 2, 3, 4].map((item, index) => {
                      return (
                        <div
                          className="col-lg-3 d-flex flex-column gap-2"
                          key={index}
                        >
                          <div className="card-title gradient-light p-3 rounded-10 d-flex justify-content-between align-items-center mb-2">
                            <h3 className="lh-1 mb-0">
                              <span
                                className="d-block placeholder text-gray"
                                style={{ width: "15rem", height: "0.5rem" }}
                              ></span>
                            </h3>
                          </div>

                          <div className="single-card p-3 border border-gray-300 rounded-10 mb-2">
                            <div className="title d-flex align-items-center gap-3 justify-content-between">
                              <p className="fs-md fw-semibold">
                                <span
                                  className="d-block placeholder text-gray"
                                  style={{ width: "15rem", height: "0.5rem" }}
                                ></span>
                              </p>
                              <div className="d-flex ms-auto">
                                <span
                                  className="d-block placeholder text-gray"
                                  style={{
                                    width: "0.1rem",
                                    height: "0.5rem",
                                  }}
                                ></span>
                              </div>
                            </div>
                            <p className="fs-sm text-gray d-flex align-items-center gap-1 my-3">
                              <span className="d-block material-symbols-outlined icon-sm">
                                calendar_month
                              </span>
                              <span
                                className="d-block placeholder"
                                style={{ width: "5rem", height: "0.5rem" }}
                              ></span>
                              <span
                                className="d-block placeholder ms-2"
                                style={{ width: "5rem", height: "0.5rem" }}
                              ></span>
                            </p>
                            <div className="d-flex align-items-center gap-3">
                              <div className="profile d-flex align-items-center gap-2">
                                <div
                                  className="avatar rounded-circle overflow-hidden"
                                  style={{
                                    width: "22px",
                                    height: "22px",
                                  }}
                                >
                                  <img
                                    src={assetImages.defaultUser}
                                    alt="User"
                                    className="w-100 h-100 object-fit-cover object-center"
                                  />
                                </div>
                                <p className="fs-sm">
                                  <span
                                    className="d-block placeholder text-gray"
                                    style={{
                                      width: "5rem",
                                      height: "0.5rem",
                                    }}
                                  ></span>
                                </p>
                              </div>
                              <p className="d-flex align-items-center gap-1">
                                <span className="d-block material-symbols-outlined icon-sm">
                                  schedule
                                </span>
                                <span
                                  className="d-block fs-sm placeholder text-gray"
                                  style={{ width: "5rem", height: "0.5rem" }}
                                ></span>
                              </p>
                              <span
                                className="d-inline-block fs-xs lh-1 text-white px-2 py-1 bg-primary rounded-90 placeholder"
                                style={{ width: "5rem", height: "1rem" }}
                              ></span>
                            </div>
                          </div>
                          <div className="single-card p-3 border border-gray-300 rounded-10 mb-2">
                            <div className="title d-flex align-items-center gap-3 justify-content-between">
                              <p className="fs-md fw-semibold">
                                <span
                                  className="d-block placeholder text-gray"
                                  style={{ width: "15rem", height: "0.5rem" }}
                                ></span>
                              </p>
                              <div className="d-flex ms-auto">
                                <span
                                  className="d-block placeholder text-gray"
                                  style={{
                                    width: "0.1rem",
                                    height: "0.5rem",
                                  }}
                                ></span>
                              </div>
                            </div>
                            <p className="fs-sm text-gray d-flex align-items-center gap-1 my-3">
                              <span className="d-block material-symbols-outlined icon-sm">
                                calendar_month
                              </span>
                              <span
                                className="d-block placeholder"
                                style={{ width: "5rem", height: "0.5rem" }}
                              ></span>
                              <span
                                className="d-block placeholder ms-2"
                                style={{ width: "5rem", height: "0.5rem" }}
                              ></span>
                            </p>
                            <div className="d-flex align-items-center gap-3">
                              <div className="profile d-flex align-items-center gap-2">
                                <div
                                  className="avatar rounded-circle overflow-hidden"
                                  style={{
                                    width: "22px",
                                    height: "22px",
                                  }}
                                >
                                  <img
                                    src={assetImages.defaultUser}
                                    alt="User"
                                    className="w-100 h-100 object-fit-cover object-center"
                                  />
                                </div>
                                <p className="fs-sm">
                                  <span
                                    className="d-block placeholder text-gray"
                                    style={{
                                      width: "5rem",
                                      height: "0.5rem",
                                    }}
                                  ></span>
                                </p>
                              </div>
                              <p className="d-flex align-items-center gap-1">
                                <span className="d-block material-symbols-outlined icon-sm">
                                  schedule
                                </span>
                                <span
                                  className="d-block fs-sm placeholder text-gray"
                                  style={{ width: "5rem", height: "0.5rem" }}
                                ></span>
                              </p>
                              <span
                                className="d-inline-block fs-xs lh-1 text-white px-2 py-1 bg-primary rounded-90 placeholder"
                                style={{ width: "5rem", height: "1rem" }}
                              ></span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                // show kanban board
                <div className="board_outer">
                  <div className="project-tasks-container d-flex overflow-x-auto pb-3 grid-view gap-2">
                    <DndContext
                      announcements={defaultAnnouncements}
                      sensors={sensors}
                      collisionDetection={closestCorners}
                      onDragStart={handleDragStart}
                      onDragOver={handleDragOver}
                      onDragEnd={handleDragEnd}
                      onDragMove={handleDragMove}
                    >
                      {items.map((container) => (
                        <BoardColumn
                          key={container.id}
                          id={container.id}
                          items={container.items}
                          columnlabel={container.label}
                          setReloadData={setReloadData}
                          collapseColumnTags={collapseColumnTags}
                          setCollapseColumnTags={setCollapseColumnTags}
                          setSelectedComponentId={setSelectedComponentId}
                          setSelectedTagId={setSelectedTagId}
                        />
                      ))}

                      <DragOverlay>
                        {activeId && findContainer(activeId) ? (
                          <ColumnItem
                            id={activeId}
                            item={findContainer(activeId).items.find(
                              (item) => item.id === activeId
                            )}
                          />
                        ) : null}
                      </DragOverlay>
                    </DndContext>
                  </div>
                </div>
              )
            ) : (
              // show empty process message
              <div className="challenges_empty text-center">
                <div className="empty_pic mb-4">
                  {" "}
                  <img src={assetImages.emptyVector} alt="" />
                </div>
                <div className="empty_text">
                  <p className="fs-lg text-gray fw-semibold mb-4">
                    Select and existing process or create a new one
                  </p>
                  <div className="d-flex align-items-center justify-content-center gap-2">
                    <Link
                      to="#kanbanExample"
                      className="btn btn-outline-primary d-flex align-items-center gap-1"
                      data-bs-toggle="modal"
                      data-bs-target="#kanbanExample"
                      role="button"
                      aria-controls="saveProcess"
                    >
                      <span className="d-block material-symbols-outlined icon-md">
                        add
                      </span>
                      <span className="d-block">
                        Add Process From Templates
                      </span>
                    </Link>
                    <Link
                      to="#activityProcessOffCanvas"
                      data-bs-toggle="offcanvas"
                      role="button"
                      aria-controls="saveProcess"
                      className="btn btn-primary d-inline-flex align-items-center gap-1"
                    >
                      <span className="d-block material-symbols-outlined icon-md">
                        add
                      </span>
                      <span className="d-block">Add Process</span>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/*Add or update custom process or board  */}

      {addNewBoard ? (
        <SaveActivityProcessPopup
          moduleSlug={moduleSlug}
          afterPopupClose={addEditBoardHandler}
          isTemplate={false}
          setShowAlert={setShowAlert}
          setAlertMessage={setAlertMessage}
          setMessageType={setMessageType}
          setRequestBoardUpdate={setRequestBoardUpdate}
        />
      ) : (
        <SaveActivityProcessPopup
          moduleSlug={moduleSlug}
          afterPopupClose={addEditBoardHandler}
          setSelectProcessId={() => {
            setSelectedProcessId;
          }}
          selectedProcessId={selectedProcessId}
          isTemplate={false}
          setShowAlert={setShowAlert}
          setAlertMessage={setAlertMessage}
          setMessageType={setMessageType}
          requestBoardUpdate={requestBoardUpdate}
          setRequestBoardUpdate={setRequestBoardUpdate}
        />
      )}

      {/* Add process from template */}
      <AddProcessFromTemplateModal
        moduleSlug={moduleSlug}
        afterProcessModalClose={afterProcessModalClose}
        setShowAlert={setShowAlert}
        setAlertMessage={setAlertMessage}
        setMessageType={setMessageType}
      />

      <TaskManagementPopup
        selectedTags={[selectedTagId]}
        projectId={null}
        leadId={null}
        contactId={null}
        selectedTaskModerator={userInfo._id}
        selectedSectionValue={null}
        sectionOptions={[]}
        memberList={[]}
        selectedTaskId={selectedComponentId}
        onPopUpClose={() => {
          setSelectedComponentId(null);
        }}
        getTaskList={() => {
          setTimeout(() => {
            setReloadData(true);
          }, 1500);
        }}
        setShowAlert={setShowAlert}
        setAlertMessage={setAlertMessage}
        setMessageType={setMessageType}
      />

      <TaskAdvanceFilterPopup
        setJsonFilterQuery={setJsonFilterQuery}
        moduleSlug="mytaskkanban"
        reloadList={resetFilterData}
        isFilterReset={isFilterReset}
        setIsFilterReset={setIsFilterReset}
      />

      {showAlert && (
        <AlertNotification
          showAlert={showAlert}
          message={alertMessage}
          alertType={messageType}
          onClose={onAlertClose}
        />
      )}
    </div>
  );
};

export default MyTaskKanbanKitBody;
