/* eslint-disable */
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { MaterialReactTable } from "material-react-table";

import AutomationHeader from "components/AutomationComponents/AutomationConfig/AutomationHeader/AutomationHeader";

import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import {automationHeaderLinks} from "helper/AutomationHelper/AutomationMenuLinks";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import typeOptions from "data/Automation/Type.json";
import actionOptions from "data/Automation/Actions.json";

const AutomationListBody = () => {

    const token = localStorage.getItem("token");
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    const moduleAccess = localStorage.getItem("moduleaccess");
    const { t, i18n } = useTranslation(); //for translation

    const commonHeaderObject = automationHeaderLinks(t);

    const [isLoading, setIsLoading] = useState(false);
    const [automationList, setAutomationList] = useState([]);

    //optionally, you can manage the row selection state yourself
    const [rowSelection, setRowSelection] = useState({});
    const [selectedItemIds, setSelectedItemIds] = useState([]);
    const [selectedAutomationId, setSelectedAutomationId] = useState(null);

    const [reloadData, setReloadData] = useState(false);

    // Pagination -------------------------------------------
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10, //customize the default page size
    });

    const [lastPagination, setLastPagination] = useState({
        pageIndex: 0,
        pageSize: 10, //customize the default page size
    });

    //alert requirements
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState("");
    const [messageType, setMessageType] = useState("");

    

  //get all automation
  const getAllAutomation = async () => {

    setSelectedItemIds([]);
    setRowSelection({});

    try {
      setIsLoading(true);

      let requestUrl =
        url.API_BASE_URL + url.API_AUTOMATION_CONFIG + `?token=${token}`;

        console.log(`getAllAutomation requestUrl--->`, requestUrl);

        const response = await getData(requestUrl);

        console.log("response in automation list------->", response);
        setIsLoading(false);

        if (response.status) {
            setMessageType("success");
            setAutomationList(response.data);
        } else {
            setMessageType("error");
        }

        setAlertMessage(response.message);

    } catch (error) {
        console.error("Error fetching automation list------->", error);
        setAlertMessage(error.message);
        setMessageType("error");
    }

        setShowAlert(true);
  };

  //* Function for reset filter
  const resetFilterData = () => {

    setReloadData(true);
    // reset pagination to default
    setLastPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
  };



  useEffect(() => {
    getAllAutomation();
  }, []);

  //  reloadData effect
  useEffect(() => {
    if (reloadData) {
      getAllAutomation();
      setReloadData(false);
    }
  }, [reloadData]);


   //** Material React Table Column and States
  const columns = useMemo(
    () => [
      {
        accessorKey: '#',
        header: t('Actions'),
        Cell: ({ row }) => (
          <Link
            className="action_btn_mui"
            to={`/admin/automation/save/${row.original._id}`}
          >
            <span className="d-block material-symbols-outlined horz_icon">
              more_horiz
            </span>
          </Link>
        ),
        enableColumnActions: false, // Hides the column action icon
        enableColumnDragging: false, // Hides the move icon
        enableSorting: false,
      },
      {
        accessorKey: 'trigger',
        header: t('Trigger Module'),
        Cell: ({ row }) => (
          <p>
            <span className="d-block">{row.original.trigger.module?.name??''}</span>
          </p>
        ),
      },
      {
        accessorKey: 'trigger.type',
        header: t('Trigger Type'),
        Cell: ({ row }) => {
          const triggerType = row.original.trigger.type;
          const matchedType = typeOptions.find(option => option.value === triggerType);
          return (
            <p>
              <span className="d-block">{matchedType ? matchedType.label : triggerType}</span>
            </p>
          );
        },
      },
      {
        accessorKey: 'trigger.value',
        header: t('Trigger Value'),
        Cell: ({ row }) => (
          <p>
            <span className="d-block">{row.original.trigger.tag?.label??''}</span>
          </p>
        ),
      },
      {
        accessorKey: 'steps',
        header: t('Steps'),
        size: 800, // Increase width to accommodate table structure
        Cell: ({ row }) => (
          row.original.steps.length > 0 ? (
            <table className="table table-bordered table-sm">
              <tbody>
                {row.original.steps.map((step, index) => {
                  const matchedAction = actionOptions.find(option => option.value === step.action);
                  let value = '';
                  let reference = '';
                  switch (step.action) {
                    case 'wait':
                      value = step.duration;
                      reference = 'minutes';
                      break;
                    case 'send_email':
                    case 'send_notification':
                      value = step.messagetemplate?.name?? t('No template specified');
                      reference = 'mail template'
                      break;
                    case 'add_tag':
                    case 'remove_tag':
                      value = step.tag?.title?? t('No tag specified');
                      reference = 'label';
                      break;
                    case 'change_status':
                      value = step.parameters == "0" ? t('Disable') : t('Enable');
                      reference = '';
                      break;
                    default:
                      value = step.parameters || t('No parameters specified');
                  }
                  return (
                    <tr key={index}>
                      <td>Step {step.order}</td>
                      <td>{matchedAction ? matchedAction.label : step.action}</td>
                      <td>
                        {value} <span style={{ fontSize: "0.8em", color: "#6c757d" }}>{reference}</span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <p className="text-muted">{t('No steps defined')}</p>
          )
        ),
      },

      {
        accessorKey: 'owner',
        header: t('Owner'),
        size: 300, // Increase width to accommodate owner information
        Cell: ({ row }) => (
          <div className="owner border-bottom-0 d-flex gap-1">
            <img
              className="rounded-circle"
              height={40}
              width={45}
              src={
                row.original.owner.image == ''
                  ? assetImages.defaultUser
                  : url.SERVER_URL + row.original.owner.image
              }
              alt="Babcock"
            />
            <p>
              <span className="d-block fw-bold">
                {row.original.owner.name}
              </span>
              <span className="d-block">{row.original.owner.email}</span>
            </p>
          </div>
        ),
      },
      
    
    ],
    [i18n.language]
  );

  //initialize the column order
  const initialColumnOrder = [
    "mrt-row-actions",
    "mrt-row-select",
    ...columns.map((c) => c.accessorKey),
  ]; //array of column ids (Initializing is optional as of v2.10.0)

  const [columnOrder, setColumnOrder] = useState(initialColumnOrder);

  //refresh icon function
  const refreshRecords = () => {
    resetFilterData();
    //setVisibleColoumns(initialVisibilityState);
    setColumnOrder(initialColumnOrder);
  };

  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" 
  ) {
    return (
      <div id="content_wrapper">
        <section className="survey-wrapper bg-white pb-5">

         <TabsHeader
              commonHeaderObject={commonHeaderObject}
              activeOption={t("Automation Config")}
            />

          <div className="container-fluid px-lg-5">

            <AutomationHeader reloadList={refreshRecords} />

            {isLoading ? (
              <div className="placeholder-glow d-flex flex-column gap-4">
                <span className="placeholder placeholder-lg bg-secondary col-12"></span>
                <span className="placeholder placeholder-lg bg-secondary col-8"></span>
                <span className="placeholder placeholder-lg bg-secondary col-4"></span>
              </div>
            ) : (
              <div className="table-wrapper">
                <MaterialReactTable
                  columns={columns} // map columns to be displayed with api data,
                  data={automationList} // data from api to be displayed
                  enableGrouping
                  enableRowSelection // enable showing checkbox
                  getRowId={row => row._id} // map which value to select with row checkbox
                  onRowSelectionChange={setRowSelection} //connect internal row selection state to your own
                  state={{ rowSelection, columnOrder }} //pass our managed row selection state to the table to use
               
                  muiTableContainerProps={{
                    sx: {
                      maxHeight: "60vh",
                    },
                  }}
                  enableStickyHeader
                />
              </div>
            )}
          </div>
        </section>

        {/* ========= popups and modals area ======== */}

        {/* {showAlert && (
          <AlertNotification
            showAlert={showAlert}
            message={alertMessage}
            alertType={messageType}
            onClose={onAlertClose}
          />
        )} */}
      </div>
    );
  } else {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper bg-white pb-5">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-lg text-gray fw-semibold mb-4">
                {t("Sorry....! You don't have privilege to see this content")}
              </p>
            </div>
          </div>
        </section>
      </div>
    );
  }


}

export default AutomationListBody;