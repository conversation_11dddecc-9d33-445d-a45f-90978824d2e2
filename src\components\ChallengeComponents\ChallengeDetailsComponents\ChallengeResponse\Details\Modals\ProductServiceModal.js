/* eslint-disable */
import React, { useState, useEffect } from "react";

const ProductServiceModal = ({
  capitalQuestResponseData,
  setCapitalQuestResponseData,
}) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  // Update formData whenever capitalQuestResponseData changes
  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      productdescription: formData.productdescription,
      currentdevelopmentstage: formData.currentdevelopmentstage,
      productdevelopmentroadmap: formData.productdevelopmentroadmap,
      technology: formData.technology,
      intellectualproperty: formData.intellectualproperty,
      productcompetitiveadvantage: formData.productcompetitiveadvantage,
    }));
    
    let modal = document.querySelector("#product_service_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="product_service_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Product/Service Information</h3>
                <h5>Provide details about your product or service</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Product Description</label>
                    <textarea
                      className="form-control"
                      name="productdescription"
                      value={formData.productdescription || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Product Description"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Current Development Stage</label>
                    <textarea
                      className="form-control"
                      name="currentdevelopmentstage"
                      value={formData.currentdevelopmentstage || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Current Development Stage"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Product Development Roadmap</label>
                    <textarea
                      className="form-control"
                      name="productdevelopmentroadmap"
                      value={formData.productdevelopmentroadmap || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Product Development Roadmap"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Technology</label>
                    <textarea
                      className="form-control"
                      name="technology"
                      value={formData.technology || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Technology Details"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Intellectual Property</label>
                    <textarea
                      className="form-control"
                      name="intellectualproperty"
                      value={formData.intellectualproperty || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Intellectual Property Details"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Product Competitive Advantage</label>
                    <textarea
                      className="form-control"
                      name="productcompetitiveadvantage"
                      value={formData.productcompetitiveadvantage || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Product Competitive Advantage"
                    ></textarea>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductServiceModal;
