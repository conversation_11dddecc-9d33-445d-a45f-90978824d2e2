/* eslint-disable */
import React from "react";
import { Link } from "react-router-dom";
import BreadCrumb from "components/Common/BreadCrumb/BreadCrumb";
import { useTranslation } from "react-i18next";

const AutomationHeader = ({ reloadList = () => {} }) => {
  /* ---- bread crumb text ---- */
  const breadcrumbText = [{ title: "Automation" }];

  const { t } = useTranslation();

  return (
    <div className="filter-container py-3">
      <div className="row align-items-center">
        <div className="col-md-2 col-lg-2 d-none d-md-block mb-3 mb-md-0">
          {/* --- breadcrumb start --- */}
          <BreadCrumb breadCrumbText={breadcrumbText} top={false} />
          {/* --- breadcrumb end --- */}
        </div>
        <div className="col-md-10 col-lg-10 d-flex justify-content-end gap-1 gap-sm-2">
          <Link
            onClick={reloadList}
            to="#"
            className="btn btn-gray d-flex align-items-center"
          >
            <span className="d-block material-symbols-outlined icon-md">
              refresh
            </span>
          </Link>

          

          {/* ---- add new event section start ---- */}
          <Link
            to="/admin/automation/save"
            className="btn btn-primary d-flex align-items-center gap-1"
          >
            <span className="d-block material-symbols-outlined icon-md">
              add
            </span>
            <span className="d-block">{t("Add New")}</span>
          </Link>
          {/* ---- add new event section end ---- */}


        </div>
      </div>
    </div>
  );
};

export default AutomationHeader;
