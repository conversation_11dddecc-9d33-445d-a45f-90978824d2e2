/* eslint-disable */
import Header from "components/Common/Header/Header";
import EventFeedbackRulesBody from "components/EventComponents/EventDetailsComponents/EventFeedbackComponents/FeedbackRules/EventFeedbackRulesBody";

const EventFeedbackRules = () => {
  return (
    <main id="app">
      {/* ---- header start ---- */}
      <Header moduleName="events" />
      {/* --- header end --- */}

      <EventFeedbackRulesBody />
    </main>
  );
};

export default EventFeedbackRules;
