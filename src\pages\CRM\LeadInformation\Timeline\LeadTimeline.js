/* eslint-disable */
import React, { useEffect } from "react";
import Header from "components/Common/Header/Header";
import LeadTimelineBody from "components/CRMComponents/CRMLeadInformationComponents/Timeline/LeadTimelineBody";

const LeadTimeline = () => {
  useEffect(() => {
    document.title = "CRM Lead Information | Timeline";
  }, []);

  return (
    <main id="app">
      <Header moduleName="crm" />
      <LeadTimelineBody />
    </main>
  );
};

export default LeadTimeline;
