/* eslint-disable */
import React, { useEffect, useRef, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData, uploadMultipleFile } from "utils/Gateway";

import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import { eventDetailsHeaderLinks } from "helper/EventHelper/EventHelper";
import AccessDeniedView from "components/Common/AccessDeniedView/AccessDeniedView";
import BreadCrumb from "components/Common/BreadCrumb/BreadCrumb";
import EventFeedbackGivenHeader from "../Header/EventFeedbackGivenHeader";

const EventFeedbackGivenBody = () => {
  const { t } = useTranslation(); //for translation
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");

  const breadCrumbText = [
    { title: t("Event"), link: "/admin/events/list" },
    { title: t("Gallery") },
  ];

  const [commonHeaderObject, setCommonHeaderObject] = useState([]); // State for header links
  const [parentEventTitle, setParentEventTitle] = useState("");

  const [isEventModerator, setisEventModerator] = useState(false);

  const [eventMemberStatus, setEventMemberStatus] = useState("");
  const [eventModStatus, setEventModStatus] = useState("");
  const [isEventDetailsLoading, setIsEventDetailsLoading] = useState(false);

  //* alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  //function for get details of event member
  const getMemberDetails = async () => {
    setIsEventDetailsLoading(true);
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_EVENT_MEMBER_DETAILS_BY_QUERY +
        `?token=${token}&eventid=${params.id}`;

      const response = await getData(requestUrl);

      console.log("response in member details", response);

      if (response.status) {
        setEventMemberStatus(response.data.memberstatus);
        setEventModStatus(response.data.moderatorstatus);
      } else {
        setAlertMessage(response.message);
        setMessageType("error");
        setShowAlert(true);
      }

      setIsEventDetailsLoading(false);
    } catch (error) {
      setAlertMessage(error.message);
      setMessageType("error");
      setShowAlert(true);
    }
  };

  //function for get event details
  const getEventDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_EVENT_DETAILS +
        `/${params.id}?token=${token}`;

      console.log("url of event gallery------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response of event gallery------>", response);

      if (response.status && response.data) {
        setParentEventTitle(response.data?.title);

        // Assign slug and update header links
        // response.data?.slug && setEventSlug(response.data?.slug);
        // set event type slug
        setCommonHeaderObject(
          eventDetailsHeaderLinks(params.id, t, response.data?.eventtype?.slug)
        );

        let isModerator = false;

        if (
          response.data.moderator._id.toString() === userInfo._id.toString() ||
          userInfo.role.slug === "ADMIN" ||
          userInfo.role.slug === "SUPER_ADMIN"
        ) {
          isModerator = true;
        } else {
          getMemberDetails();
        }

        setisEventModerator(isModerator);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id) {
      getEventDetails();
    }
  }, [params.id]);

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_EVENT")
  ) {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper crm-conversation-wrapper bg-white pb-5">
          {/* ---- common header ---- */}
          {params.id && (
            <TabsHeader
              commonHeaderObject={commonHeaderObject}
              activeOption={t("Feedback 360")}
            />
          )}
          <div className="container-fluid px-lg-5 pt-4 pt-md-0">
            <BreadCrumb
              breadCrumbText={breadCrumbText}
              bottom={true}
              displayName={`${parentEventTitle}`}
            />

            <EventFeedbackGivenHeader />
          </div>
        </section>
      </div>
    );
  } else {
    return <AccessDeniedView />;
  }
};

export default EventFeedbackGivenBody;
