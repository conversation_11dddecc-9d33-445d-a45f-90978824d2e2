# Findeloi v2 Frontend

## Description

This repository contains the frontend application for Findeloi v2, a comprehensive platform built with React. It provides a user-friendly interface for managing various aspects of the Findeloi system, including administration, user management, marketplace, project management, and more.

## Features

- **Modular Component Architecture**: Organized into various components for reusability and maintainability.
- **Multi-language Support**: Includes internationalization capabilities with `i18n`.
- **Comprehensive Dashboard**: Provides an overview of key metrics and activities.
- **Extensive Module Support**: Covers functionalities like CRM, Invoicing, Project Management, Challenges, and more.

## Installation

To set up the project locally, follow these steps:

1.  **Clone the repository**:

    ```bash
    git clone https://github.com/your-username/findeloi-v2-backend.git
    cd findeloi-v2-backend
    ```

    _(Note: The repository name `findeloi-v2-backend` suggests this might be a backend, but the file structure indicates a React frontend. I'm assuming this is the frontend part of a larger system. The `git clone` command should be updated to the correct repository URL once known.)_

2.  **Install dependencies**:
    ```bash
    npm install
    # or
    yarn install
    ```

## Usage

To run the application in development mode:

```bash
npm start
# or
yarn start
```

This will open the application in your browser at `http://localhost:3000`. The page will reload if you make edits.

## Project Structure

The project follows a component-based architecture, with key directories:

- `public/`: Contains static assets and the main `index.html` file.
- `src/`: Contains the core application logic.
  - `src/App.js`: Main application component.
  - `src/index.js`: Entry point of the React application.
  - `src/assets/`: Static assets like images.
  - `src/components/`: Reusable UI components, categorized by feature (e.g., `AdministrationComponents`, `CRMComponents`).
  - `src/Config/`: Configuration files.
  - `src/constants/`: Application-wide constants.
  - `src/context/`: React Context API for global state management.
  - `src/data/`: Local data (e.g., JSON files for countries, currencies).
  - `src/helper/`: Utility functions and helper modules.
  - `src/locales/`: Localization files for different languages.
  - `src/pages/`: Top-level page components, corresponding to different routes.
  - `src/routes/`: Defines application routes.
  - `src/translations/`: Internationalization setup.
  - `src/utils/`: General utility functions.

## Technologies Used

- React
- Node.js (for npm/yarn)
- Material-UI (`@mui/material`, `@mui/icons-material`)
- Axios
- React Router DOM
- i18next & react-i18next (Internationalization)
- CKEditor 5
- Dnd-kit (Drag and Drop)
- Material React Table
- React Big Calendar
- XLSX (Excel file handling)

## Contributing

Contributions are welcome! Please follow these steps:

1.  Fork the repository.
2.  Create a new branch (`git checkout -b feature/YourFeature`).
3.  Make your changes.
4.  Commit your changes (`git commit -m 'Add some feature'`).
5.  Push to the branch (`git push origin feature/YourFeature`).
6.  Open a Pull Request.

## License

This project is licensed under the MIT License.
