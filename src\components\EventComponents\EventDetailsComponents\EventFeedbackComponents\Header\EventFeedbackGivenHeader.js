/* eslint-disable */
import React from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

const EventFeedbackGivenHeader = ({ reloadList = () => {} }) => {
  const { t } = useTranslation();
  const params = useParams();

  return (
    <div className="filter-container py-3">
      <div className="row align-items-center">
        {/* ---- search start ---- */}
        <div className="col-md-5 d-none d-md-block">
          <ul
            className="nav nav-tabs d-inline-flex p-1 body-bg flex-nowrap fs-md lh-1 fw-semibold border-0 text-nowrap rounded-10"
            id="mailTab"
          >
            <li className="nav-item" role="presentation">
              <Link
                to={`/admin/events/feedbackrules/${params.id}`}
                className="text-black d-flex align-items-center justify-content-center gap-2 rounded-10 "
              >
                <span className="d-block material-symbols-outlined icon-lg">
                  archive
                </span>
                <span className="d-block">{t("Feedback Rules")}</span>
              </Link>
            </li>
            <li className="nav-item" role="presentation">
              <Link
                to={`/admin/events/feedbackgiven/${params.id}`}
                className="text-black d-flex align-items-center justify-content-center gap-2 rounded-10 active"
              >
                <span className="d-block material-symbols-outlined icon-lg">
                  unarchive
                </span>
                <span className="d-block">{t("Feedback Given")}</span>
              </Link>
            </li>
          </ul>
        </div>
        {/* ---- search end ---- */}

        <div className="col-md-7 d-flex justify-content-end gap-1 gap-sm-2">
          <Link
            to="#"
            className="btn btn-gray d-flex align-items-center"
            onClick={reloadList}
          >
            <span className="d-block material-symbols-outlined icon-md">
              refresh
            </span>
          </Link>
          {/* --- action start --- */}
          <div className="dropdown flex-fill flex-grow-sm-0">
            <button
              className="btn btn-gray text-start w-100 dropdown-toggle"
              type="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
            >
              Action
            </button>
            <ul className="dropdown-menu w-100 bg-white fs-sm fw-semibold border-0 rounded-10 shadow-sm">
              <li>
                <Link
                  to="#"
                  className="dropdown-item d-flex align-items-center gap-2"
                  data-bs-toggle="modal"
                  data-bs-target="#addLabel"
                >
                  <span className="d-block material-symbols-outlined icon-sm">
                    label
                  </span>
                  <span className="d-block">Add Label</span>
                </Link>
              </li>
              <li>
                <Link
                  to="#"
                  className="dropdown-item d-flex align-items-center gap-2"
                >
                  <span className="d-block material-symbols-outlined icon-sm">
                    delete
                  </span>
                  <span className="d-block">Delete</span>
                </Link>
              </li>
            </ul>
          </div>
          {/* --- action end --- */}

          <Link
            to="#"
            className="btn btn-gray d-flex align-items-center gap-1"
            type="button"
            data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasLabelFilter"
            aria-controls="offcanvasLabelFilter"
          >
            <span className="d-block material-symbols-outlined icon-md">
              tune
            </span>
            <span className="d-block">{t("Filter")}</span>
          </Link>
          <Link
            to="#addUpdateLabelOffCanvas"
            className="btn btn-primary d-flex align-items-center gap-1"
            data-bs-toggle="offcanvas"
            role="button"
            aria-controls="offcanvasLabelDetails"
          >
            <span className="d-block material-symbols-outlined icon-md">
              add
            </span>
            <span className="d-block">{t("Add New")}</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default EventFeedbackGivenHeader;
