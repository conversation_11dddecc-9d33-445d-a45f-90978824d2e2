import React, { useEffect } from "react";
import Header from "components/Common/Header/Header";
import AutomationListBody from "components/AutomationComponents/AutomationConfig/AutomationListBody/AutomationListBody";

const AutomationList = () => {
  // -------- for title ------------
  useEffect(() => {
    document.title = "Automation List";
  }, []);

  return (
    <main id="app">
      {/* ---- common header ---- */}
      <Header moduleName="automation" />

      <AutomationListBody />
    </main>
  );
};

export default AutomationList;
