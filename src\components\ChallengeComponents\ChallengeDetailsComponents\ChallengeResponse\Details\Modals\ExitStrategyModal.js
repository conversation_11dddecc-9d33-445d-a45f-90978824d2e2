/* eslint-disable */
import { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';

const ExitStrategyModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  // Initialize formData with the data from capitalQuestResponseData
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  // Update formData whenever capitalQuestResponseData changes
  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSectorChange = (newValue) => {
    const sectors = newValue ? newValue.map(item => item.value) : [];
    setFormData({ ...formData, exitinsector: sectors });
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      potentialacquirers: formData.potentialacquirers,
      timeframe: formData.timeframe,
      exitinsector: formData.exitinsector,
      estimatereturn: formData.estimatereturn
    }));
    let modal = document.querySelector("#exit_strategy_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  const sectorOptions = formData.exitinsector.map(sector => ({
    value: sector,
    label: sector
  }));

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="exit_strategy_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Exit Strategy</h3>
                <h5>Provide details about your exit strategy</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Potential Acquirers</label>
                    <textarea
                      className="form-control"
                      name="potentialacquirers"
                      value={formData.potentialacquirers}
                      onChange={handleChange}
                      placeholder="List potential acquiring companies"
                      rows="3"
                    />
                  </div>
                  <div className="form-group">
                    <label>Timeframe</label>
                    <input
                      type="text"
                      className="form-control"
                      name="timeframe"
                      value={formData.timeframe}
                      onChange={handleChange}
                      placeholder="Expected exit timeframe"
                    />
                  </div>
                  <div className="form-group">
                    <label>Exit in Sectors</label>
                    <CreatableSelect
                      isMulti
                      value={sectorOptions}
                      onChange={handleSectorChange}
                      placeholder="Add target sectors for exit"
                      formatCreateLabel={(inputValue) => `Add sector "${inputValue}"`}
                    />
                  </div>
                  <div className="form-group">
                    <label>Estimated Return</label>
                    <input
                      type="text"
                      className="form-control"
                      name="estimatereturn"
                      value={formData.estimatereturn}
                      onChange={handleChange}
                      placeholder="Expected return on investment"
                    />
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExitStrategyModal;
