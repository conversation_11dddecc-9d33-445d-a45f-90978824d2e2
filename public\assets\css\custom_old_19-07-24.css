img{max-width: 100%;max-height: 100%;}

.left-process-select{min-width: 296px;}
header#header .offcanvas .offcanvas-body  ul.nav li.opened .submenu{opacity: 1;visibility: visible;height: auto;}
.single-card-scroll{max-height: 400px;overflow-y: auto;}
.challenges_empty{padding: 60px 0 100px;}
.text-primary{color: var(--bs-primary);}
.login_tabs ul {border: 0;margin: 0 0 24px;flex-wrap: nowrap; overflow-x: auto; overflow-y: hidden;}
.login_tabs ul li a {padding: 14px 10px;position: relative;border: 0 !important;font-weight: 600; font-size: 14px;color: var(--bs-gray);
font-family: 'Titillium Web', sans-serif; white-space: nowrap; text-transform: capitalize;background-color: transparent !important;border-bottom: 3px solid var(--bs-gray-200) !important;margin: 0;}
.login_tabs ul li a.active { border-bottom-color: var(--bs-primary) !important;}
.login_tabs .nav-tabs .nav-item.show .nav-link, .login_tabs .nav-tabs .nav-link.active{background-color: transparent;}
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}

.usr_img{height: 23px;width: 23px;min-width: 23px;}
.text-green{color: var( --bs-success);}
.task_info_row > label{min-width: 103px;}
.member-box{box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18);}
.members-pic{height: 40px;width: 40px;min-width: 40px;}
.project-members-container .row{margin: 0 -5px;}
.project-members-container .col-sm-6{padding: 0 5px;}


.card-usr{height: 30px;width: 30px;min-width: 30px;}
.card-usr img{height: 100%;width: 100%;object-fit: cover;}
.mail-icon{max-width: 136px;margin-left: auto;margin-right: auto;}

.project-tasks-container .accordion-body{padding-left: 35px !important;}


/* ------------------- */
.graph_top_value_row{border: 1px solid var(--bs-gray-300);border-radius: 4px;padding: 16px 24px;}
.graph_top_value_row .col{width: 25%;margin-right: 40px;padding-right: 40px;border-right: 1px solid var(--bs-gray-300);}
.graph_top_value_row .col:last-child{border-right: 0;padding-right: 0;margin-right: 0;}
.graph_value_item h3 span{vertical-align: middle;}
.graph_pic img{max-width: 100%;max-height: 100%;}


.total_count{border: 1px solid var(--bs-gray-300);box-shadow: 0 0 10px rgba(0,0,0,0.15);border-radius: 0 0 10px 10px;}
.counting_arws a, .challenges_collapse a{line-height: 100%;}
.counting_arws a span, .challenges_collapse a span{line-height: 100%;}
.challenges_collapse{height: 100%;border:1px solid var(--bs-gray-300);width: 90px;background: rgb(225, 227, 250, 0.3);}
.roate_text{transform: rotate(90deg);margin-top: 40px;}
.roate_text h5{letter-spacing: 0.8px;white-space: nowrap;}
.responses-card-wrapper .cards-container{max-height: 400px;overflow-y: auto;}
.board_outer .project-tasks-container .col-lg-3{min-width: 250px;}


/* ------------- */

/* ---notification-dropdown--- */
.notification-dropdown{max-width: 350px;}
.tabs_links_wrap{padding: 8px;}
.tabs_links_wrap .nav-tabs{padding: 4px;border-radius: 12px;border: 0;}
.tabs_links_wrap .nav-tabs .nav-link{width: 50%;text-align: center;border-radius: 12px;color: var(--bs-black);}
.tabs_links_wrap .nav-tabs .nav-link.active{background-color: var( --bs-white);color: var(--bs-primary);}
.notify_row{padding: 10px 16px;border-bottom: 1px solid #DCE5F2;}
.notify_icon{min-width: 30px;height: 30px;width: 30px;}
.notify_cross a{color: #C4CED3;}
.notify_cross a:hover{color: var(--bs-primary);}
.notify_text{margin-right: auto;}
.bg-white{background-color: var(--bs-white);}
.notify_wrapper{position: relative;}
.notify_innrscroll { max-height: 300px; overflow-y: auto; }
.refresh{position: absolute;left: 50%;transform: translateX(-50%);bottom: 10px;z-index: 99;}
.refresh a{box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);height: 32px;width: 32px; }
.date_fill_outer{box-shadow: 0px 1px 0px 0px #DCE5F2; padding: 8px 16px;}
.date_fill_outer  .form-control{height: auto;box-shadow: none;padding: 0 0px 0 30px;background-image: url(../img/calendar.svg);background-position: left 6px top 50%;background-size: 15px;background-repeat: no-repeat;}
.apply_btn{background-color: transparent;border: 0;min-width: 70px;box-shadow: none;outline: none;}
.empty_today img{max-width: 100%;max-height: 100%;}
.notify_status .btn{height: 34px;font-size: 14px;}
input[type="date"]::placeholder { color: var(--bs-gray); }
/* --------------------------------------------- */

/* 15.07.2024 */
.caender_plugin img{max-width: 100%;max-height: 100%;}
.text-extra-gray{color: var(--bs-extra-gray);}
.members_avalability{border-bottom: 1px solid var(--bs-gray-300);background-color: var(--bs-white);box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);}
.filter_input .form-control{padding-left: 35px;}
.srch_filter{top: 14px;left: 10px;background-color: transparent;border: 0;padding: 0;}
.calendar_user{height: 38px;width: 38px;min-width: 38px;border: 2px solid transparent;}
.calendar_user img{width: 100%;height: 100%;object-fit: cover;}
.calendar_user_list_row{border-bottom: 1px solid var(--bs-gray-300);}


/* --my-survey----- */
.rvw_survey_body{position: fixed;top: 0;left: 0;right: 0;}
.survey_innr {height: calc(100vh - 150px);}
.survey-canvas .offcanvas-bottom{height: 100vh;}
.left_survey{width: 35%;background-color: var(--bs-color-EA);}
.right_survey{width: 65%;padding-top: 40px;padding-bottom: 40px;}
.survey_topbx{box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.25), 0px 2px 4px 0px rgba(0, 0, 0, 0.10);}
.survey-canvas .btn-close{height: 48px;width: 48px;min-width: 48px;opacity: 1;}
.survey_srch .form-control{height: 48px;box-shadow: none;border-radius: 16px;padding: 0 16px 0 38px;border: 0;background-image: url(../img/search.svg);background-repeat: no-repeat;background-position: left 10px top 50%;background-size: 24px;}
.survey_pic {height: 78px;width: 78px;margin: 0 auto;}
.survey_progress_outer{height: 5px;background-color: var(--bs-white);border-radius: 8px;}
.s_progress_bar{left: 0;top: 0;bottom: 0;position: absolute;border-radius: 8px;}
.img_rvw {width: 100px;border-radius: 10px;overflow: hidden;height: 100px;line-height: 100%;}
.survey_revw_innr{height:calc(100vh - 382px);overflow-y: auto;padding-right: 10px;}
.right_survey{height: 100vh;overflow-y: auto;}
.survey-canvas .offcanvas-body{padding: 0;overflow: hidden;}
.survey_usr_pic{height: 30px;width: 30px;min-width: 30px;}
.kitchen_info_top h2{font-size: 32px;text-transform: capitalize;font-weight: 400;}
.survey_date_outer .form-group{min-width: 150px;}
.input_fill.form-group .form-control{border: 0;padding-right: 15px;}
.survey_kitchen_info_item .upload_inner_btn{border: 2px dashed var(--bs-gray-400);}
.survey_kitchen_info_item .upload_icon{height: auto;}
.survey_kitchen_info_item .uploaded_innrBx{padding: 0;border-radius: 10px;overflow: hidden;}
.survey_kitchen_info_item .upload_pic{height: 100%;}
.survey_kitchen_info_item .upload_pic img{height: 100%;}
.survey_kitchen_info_item .delete_btn{background-color: rgba(255,255,255,1);height: 30px;width: 30px;}
.right_rvw_inr{height: 100%;}
.form_innr .form-group textarea.form-control{resize: none;height: 200px;padding: 16px;}
.survey_subheading h3{font-size: 32px;}
.text-light{color: var(--bs-color-B3) !important;}
.open_rvw_trigger{box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);border-radius: 10px;position: fixed;top: 0;left: 0;right: 0;z-index: 9;display: none !important;}
.feed_interest li a{border: 1px solid var(--bs-color-DD);color: var(--bs-gray);}
.feed_interest li a.active, .feed_interest li a:hover{border-color: var(--bs-primary);background-color: var(--bs-primary);color: var(--bs-color-white);}
.user_right .dropdown-menu li a {padding: 7px 0px;display: flex;align-items: center;font-size: 16px;gap: 8px;color: var(--bs-gray);}

.rounded-16{border-radius: 16px;}
.rounded-10{border-radius: 10px;}
.process_hdng span{width: 120px;margin: 0 auto 20px;}
.process_modal  .modal-content{background-color: var(--bs-white);}
.process_modal .close { font-size: 17px; position: absolute; right: 15px; top: 15px; height: 34px; width: 34px; z-index: 99; border: 0;}
.process_modal .close i{font-size: 20px;}
.fs-xl{font-size: 24px;}
.process_modal .form-check-input{height: 18px;width: 18px;box-shadow: none !important;outline: none;background-color: var(--bs-white);border-color: var(--bs-gray-500);}
.process_modal .form-check-input:checked { background-color: var(--bs-primary); border-color: var(--bs-primary); }
.feededit_modal .input_fill.description_fill .form-control { height: 220px !important;resize: none; }

.pdf_info i{width: 20px;min-width: 20px;}
.pdf_info i img{width: 100%;}
.file_btn { position: absolute; left: 0; right: 0; top: 0; bottom: 0; height: 100%; width: 100%; cursor: pointer; opacity: 0; }
.process_upload { height: 235px; border: 2px dashed rgba(0, 0, 0, 0.2);}
.process_upload_text span{font-size: 40px;}
.feededit_modal .rating_star .material-symbols-outlined { font-size: 40px; }
.rating_star li a{color: var(--bs-body-bg);}
.rating_star li.active a { color: var(--bs-primary); }
.question_links li a { border: 1px solid var(--bs-gray-300); }
.question_links li.selected a { border: 1px solid var(--bs-primary);color: var(--bs-white) !important;background-color: var(--bs-primary); }
.open_rvw_trigger { box-shadow: 0 0 15px rgba(0, 0, 0, 0.1); border-radius: 10px; position: fixed; top: 0; left: 0; right: 0; z-index: 9; display: none !important; }
.overlay { position: fixed; left: 0; right: 0; top: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.8); z-index: 100; display: none; }
.open-review .overlay { display: block; }
.bttm_rvw_bx  h6, .bttm_rvw_bx  h5{font-size: 15px;font-weight: 600;}
.bttm_rvw_bx  h6 span, .bttm_rvw_bx  h5  span{margin-top: 10px;font-weight: 400;font-size: 13px;}

/* --my-survey----- */

/* ---my-cv--- */

.left_survey.left_cv_info { width: 320px; min-width: 320px; position: fixed; left: 0; top: 0; bottom: 0; height: 100vh; overflow-y: auto; }
.right_survey.right_cv_info{width: 100%;margin-left: 320px;transition: 0.5s;}
.cv_wrapper strong{font-weight: 600;}
.cv_pic{min-width: 65px;height: 65px;width: 65px;}
.cv_info_row label{min-width: 100px;}
.cv_info_row ul li{list-style: disc;}
.cv_info_row ul{padding-left: 15px;}
.highlghts_item li{font-size: 14px;font-weight: 600;list-style: disc;}
.highlghts_item ul{padding-left: 15px;}
.highlights_wrapper .project_refence{padding-left: 20px;border-left: 1px solid var( --bs-extra-gray);}
.skil_table_innr table{width: 100%;}
.skil_table_innr tbody tr:nth-child(even){background-color: var(--bs-white);}
.scroll_table_innr{overflow-x: auto;}
.fw-regular{font-weight: 400;}
/* c7c7e4 */



@media(max-width:1199px){
    .graph_top_value_row .col{padding-right: 20px;margin-right: 20px;}
    .challenges-process-row{flex-wrap: wrap;}
    .left-process-select, .right-process-btn{width: 100%;}
}
@media(max-width:991px){
    .graph_top_value_row{flex-wrap: wrap;}
    .graph_top_value_row .col{width: 50%;padding: 0;margin: 0;border: 0;flex: 1 1 50%;}
    header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu{position: absolute;}
    .left_survey { width: 290px; transition: 0.4s; position: fixed; top: 0; left: 0; z-index: 999; transform: translateX(-100%); bottom: 0;}
    .open-review .left_survey { transform: translateX(0); }
    .open_rvw_trigger { display: flex !important; }
    .right_survey{width: 100%;}
    .left_survey  .p-4 { padding: 1rem !important; }
}
@media(max-width:767px){
    .challenges-process-row{flex-wrap: wrap;}
    .left-process-select, .right-process-btn{width: 100%;}
    .dev_card_collapsed { min-width: 80px !important; }
    .challenges_collapse{width: 75px;}
    .right_survey{width: 100%;}
}
@media(max-width:575px){
    .left-process-select{width: 100%;}
    .challenges-process-row{flex-wrap: wrap;}
    .challenges_empty{padding: 60px 0;}
    .graph_top_value_row{gap: 20px;}
    .graph_top_value_row .col{flex: 1 1 100%;width: 100%;}
    .left-process-select{ flex-wrap: wrap; }
    .left-process-select  .css-b62m3t-container{width: 100%;}
    li.nav-item.dropdown.notification { position: static; }
    .notification .dropdown-menu-end{right: 15px;margin-top: 0 !important;}
}
