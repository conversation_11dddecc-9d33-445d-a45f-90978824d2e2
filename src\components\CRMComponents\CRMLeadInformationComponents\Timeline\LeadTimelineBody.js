/* eslint-disable */
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import CrmLeadInformationCommonHeader from "../common/CrmLeadInformationCommonHeader";
import { assetImages } from "constants";
import AlertNotification from "components/Common/AlertNotification/AlertNotification";
import AccessDeniedView from "components/Common/AccessDeniedView/AccessDeniedView";

const LeadTimelineBody = () => {
  const params = useParams();

  const moduleAccess = localStorage.getItem("moduleaccess");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const token = localStorage.getItem("token");

  const { t } = useTranslation(); //for translation

  const [leadName, setleadName] = useState("");
  const [leadImage, setleadImage] = useState("");
  const [isAuthenticatedUser, setisAuthenticatedUser] = useState(false);

  const [timeLinesData, setTimeLinesData] = useState([]);

  const getLeadDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_LEAD_DETAILS +
        `/${params.id}?token=${token}`;

      // console.log("url of lead details------>", requestUrl);

      const response = await getData(requestUrl);

      // console.log("response of lead details------>", response);

      if (response.status) {
        if (
          response.data.moderator._id.toString() === userInfo._id.toString()
        ) {
          setisAuthenticatedUser(true);
        }

        const respondedLeadName = response.data.name
          ? `${response.data.name ?? ""} ${response.data.surname ?? ""}`
          : `${response.data.email}`;
        setleadName(respondedLeadName);

        if (response.data?.photoimage) {
          setleadImage(response.data?.photoimage?.path);
        } else {
          setleadImage("");
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const getLeadTimeline = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_TIMELINE_EVENTS +
        `?token=${token}&moduleslug=MOD_LEAD&componentid=${params.id}`;

      // console.log("url of lead timeline------>", requestUrl);  //for debugging

      const response = await getData(requestUrl);

      console.log("response of lead timeline------>", response); //for debugging

      if (response.status) {
        setTimeLinesData(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id) {
      getLeadDetails();
      getLeadTimeline();
    }
  }, [params.id]);

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    (moduleAccess.includes("MOD_CRM") && moduleAccess.includes("MOD_LEAD"))
  ) {
    return (
      <div id="content_wrapper">
        <section className="crm-contact-timeline-wrapper bg-white">
          {/* ------ lead information common header ------ */}
          <CrmLeadInformationCommonHeader
            moduleName="timeline"
            leadName={leadName}
            leadImage={leadImage}
            isAuthenticatedUser={isAuthenticatedUser}
          />
          <div className="crm-info-container py-4 py-md-5">
            <div className="container-fluid px-lg-5">
              <div
                className={
                  timeLinesData.length > 0
                    ? "p-3 p-lg-4 border border-gray-300 rounded-10 shadow-sm"
                    : "d-none"
                }
              >
                <ul className="timeline">
                  {timeLinesData.map((timeLineInfo, index) => {
                    return (
                      <li className="position-relative" key={index}>
                        <span className="d-inline-block fs-md fw-semibold px-2 py-1 bg-gray-300 rounded-90 position-sm-absolute top-0 start-0 mb-3 mb-sm-0">
                          {timeLineInfo.month}, {timeLineInfo.year}
                        </span>
                        <ul className="d-flex flex-column">
                          {timeLineInfo.timelines.map((timeData, index2) => {
                            return (
                              <li
                                className="mail position-relative"
                                key={index2}
                              >
                                <div className="icon d-inline-block p-1 gradient-light rounded-circle position-absolute start-0 top-0">
                                  <span className="d-block material-symbols-outlined">
                                    description
                                  </span>
                                </div>
                                <p className="fs-md fw-semibold mb-0">
                                  <span className="text-primary me-1">
                                    {timeData.title}
                                  </span>
                                  <span>{timeData.description}</span>
                                </p>
                                <p className="date fs-xs mb-2">
                                  {timeData.timetext}
                                </p>
                              </li>
                            );
                          })}
                        </ul>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  } else {
    <AccessDeniedView />;
  }
};

export default LeadTimelineBody;
