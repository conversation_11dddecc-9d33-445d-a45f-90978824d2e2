/* eslint-disable */
import { useHistory } from "react-router-dom";

import MailTemplateForm from "components/Common/MailTemplateForm/MailTemplateForm";
import { useTranslation } from "react-i18next";

const MailTemlplateDetailsBody = () => {
  const history = useHistory();
  const { t } = useTranslation(); //for translation
  /* ---- bread crumb text ---- */
  const breadcrumbText = [
    {
      title: t("Administration"),
      link: "/admin/administration",
    },
    {
      title: t("Mail Template"),
      link: "/admin/administration/mailtemplate/list",
    },
    { title: t("Save") },
  ];

  const afterModalClose = (pathName) => {
    history.push(pathName);
  };

  return (
    <div id="content_wrapper">
      <section className="crm-wrapper bg-white pb-5">
        <MailTemplateForm
          moduleName="administration"
          breadcrumbText={breadcrumbText}
          afterModalClose={afterModalClose}
        />
      </section>
    </div>
  );
};

export default MailTemlplateDetailsBody;
