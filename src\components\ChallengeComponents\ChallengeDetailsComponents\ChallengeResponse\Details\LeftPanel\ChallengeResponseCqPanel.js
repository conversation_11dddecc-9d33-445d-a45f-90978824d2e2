/* eslint-disable */
import { useState, useEffect, useRef } from "react";
import { Link, useParams } from "react-router-dom";


import { FormControlLabel, Switch } from "@mui/material";
import ExecutiveSummaryModal from "../Modals/ExecutiveSummaryModal";
import FoundingTeamModal from "../Modals/FoundingTeamModal";
import ProductServiceModal from "../Modals/ProductServiceModal";
import MarketAnalysisModal from "../Modals/MarketAnalysisModal";
import TractionMetricsModal from "../Modals/TractionMetricsModal";
import BusinessModelModal from "../Modals/BusinessModelModal";
import FundingRequestModal from "../Modals/FundingRequestModal";
import FinancialProjectionsModal from "../Modals/FinancialProjectionsModal";
import ExitStrategyModal from "../Modals/ExitStrategyModal";
import RisksMitigationModal from "../Modals/RisksMitigationModal";
import SubmitterInfoModal from "../Modals/SubmitterInfoModal";

//import ApplySuccessModal from "components/CvBuilder/Modals/ApplySuccessModal"; 

import AiSuggestModal from "components/Common/Modal/CvComponentModals/AiSuggestModal";

import { useTranslation } from "react-i18next";

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData, uploadSingleFile } from "utils/Gateway";

import { assetImages } from "constants";

const ChallengeResponseCqPanel = ({
    challengeResponseDetails,
    cqId,
    setReloadResponseDetails,
    createContactChatHandler,
  }) => {


        const { t } = useTranslation();
        const params = useParams();
    
        const token = localStorage.getItem("token");
        const userInfo = JSON.parse(localStorage.getItem("userInfo"));

        const [isLoading, setIsLoading] = useState(false);

        const [jobPostDetails, setJobPostDetails] = useState("");

        const [aiSuggest, setAiSuggest] = useState(
            challengeResponseDetails.aisuggest
          );
        const [askAiSuggest, setAskAiSuggest] = useState(false);


        // State variables for user information
        const [userImagePath, setUserImagePath] = useState("");
        const [userName, setUserName] = useState("");
        const [userEmail, setUserEmail] = useState("");
        const [userPhone, setUserPhone] = useState("");
        const [userPosition, setUserPosition] = useState("");
        const [userimageid, setUserImageId] = useState("");
    
        // Add state variables for public/private visibility settings
        const [publicPersonalInformation, setPublicPersonalInformation] = useState(false);
        const [publicExecutiveSummary, setPublicExecutiveSummary] = useState(false);
        const [publicFoundingTeam, setPublicFoundingTeam] = useState(false);
        const [publicAboutTheProductOrService, setPublicAboutTheProductOrService] = useState(false);
        const [publicMarketAnalysis, setPublicMarketAnalysis] = useState(false);
        const [publicTractionAndMetrics, setPublicTractionAndMetrics] = useState(false);
        const [publicBusinessModel, setPublicBusinessModel] = useState(false);
        const [publicRequestForFunding, setPublicRequestForFunding] = useState(false);
        const [publicFinancialProjections, setPublicFinancialProjections] = useState(false);
        const [publicExitStrategy, setPublicExitStrategy] = useState(false);
        const [publicRisksAndMitigation, setPublicRisksAndMitigation] = useState(false);
    
        const [submittingData, setSubmittingData] = useState(false);
    
        const defaultQuestData = {
        // challengeId
        challengeId: params.id,


    
        // User Information
        userName: "",
        userEmail: "",
        userPhone: "",
        userPosition: "",
        userImagePath: "",
        userImageId: "",
        publicProfile: false,
    
        // Executive Summary
        startupname: "",
        startupwebsite: "",
        startupemail: "",
        startuptagline: "",
        startupdescription: "",
        problemstatement: "",
        solution: "",
        uniquevalueproposition: "",
        businessmodel: "",
        currentstatus: "",
    
        //Founders
        founders: [], // founderData
    
        // Product/Service
        productdescription: "",
        currentdevelopmentstage: "",
        productdevelopmentroadmap: "",
        technology: "",
        intellectualproperty: "",
        productdemo: [],
        productcompetitiveadvantage: "",
    
        // Market Analysis
        marketsize: "",
        markettrends: "",
        targetmarket: "",
        competitors: "",
        competitiveadvantage: "",
        entrybarriers: "",
        differentiationstrategy: "",
        customeracquisitionstrategy: "",
    
        // Traction and Metrics
        mainkpis: "",
        customerevolution: "",
        growthrate: "",
        revenue: "",
        cacandcltv: "",
        stategicpartnerships: "",
        customertestimonials: [],
    
        // Business Model
        revenuesources: "",
        distrubutionchannels: "",
        grossmargin: "",
        scalabilityplan: "",
        realtimeordertracking: false,
        pricingstrategy: [],
    
        // Request for Funding
        fundingamount: "",
        proposedvalue: "",
        fundinguse: "",
        expectedrunway: "",
        previousfunding: [],
        milestones: "",
    
        // Financial Projections
        revenueprojections: "",
        breakevenpoint: "",
        mainassumptions: "",
        scenarios: "",
    
        // Exit Strategy
        potentialacquirers: "",
        timeframe: "",
        exitinsector: [],
        estimatereturn: "",
    
        // Risks and Mitigation
        businessrisks: "",
        mitigationstrategies: "",
        swotanalysis: "",
    
        // Add public/private fields to the state
        personalInformation: false,
        executiveSummary: false,
        foundingTeam: false,
        aboutTheProductOrService: false,
        marketAnalysis: false,
        tractionAndMetrics: false,
        businessModel: false,
        requestForFunding: false,
        financialProjections: false,
        exitStrategy: false,
        risksAndMitigation: false,
        };
    
        const [capitalQuestResponseData, setCapitalQuestResponseData] = useState(defaultQuestData);
    
        const [founderData, setFounderData] = useState({
        name: "",
        position: "",
        about: "",
        experience: "",
        skills: "",
        linkedin: "",
        equityholding: "",
        notableadvisors: "",
        });
        const [founderList, setFounderList] = useState([]);
        const [founderIndex, setFounderIndex] = useState(null);
    
        const [validationErrors, setValidationErrors] = useState([]);
    
        const updateFounderBlock = (founder, index) => {
        setFounderIndex(index);
        setFounderData(founder);
        };
    
        const deleteFounderBlock = (index) => {
        const updatedList = [...founderList];
        updatedList.splice(index, 1);
        setFounderList(updatedList);
        };

        const validateApplicationData = () => {
            const errors = [];
            
            // Check required personal information
            if (!userName || userName.trim() === '') {
            errors.push("Your full name is required");
            }
            
            if (!userEmail || userEmail.trim() === '') {
            errors.push("Your email address is required");
            }
            
            // Check required executive summary fields
            if (!capitalQuestResponseData.startupname || capitalQuestResponseData.startupname.trim() === '') {
            errors.push("Startup name is required");
            }
            
            if (!capitalQuestResponseData.startupdescription || capitalQuestResponseData.startupdescription.trim() === '') {
            errors.push("Startup description is required");
            }
            
            if (!capitalQuestResponseData.problemstatement || capitalQuestResponseData.problemstatement.trim() === '') {
            errors.push("Problem statement is required");
            }
            
            if (!capitalQuestResponseData.solution || capitalQuestResponseData.solution.trim() === '') {
            errors.push("Solution is required");
            }
            
            // Check founding team
            if (founderList.length === 0) {
            errors.push("At least one founder with name and role is required");
            } else {
            // Check if any founder has missing name or role
            const invalidFounders = founderList.filter(
                founder => !founder.name || founder.name.trim() === '' || !founder.position || founder.position.trim() === ''
            );
            
            if (invalidFounders.length > 0) {
                errors.push("All founders must have both name and role");
            }
            }
            
            // Check required product/service fields
            if (!capitalQuestResponseData.productdescription || capitalQuestResponseData.productdescription.trim() === '') {
            errors.push("Product description is required");
            }
            
            // Check required funding request fields
            if (!capitalQuestResponseData.fundingamount || capitalQuestResponseData.fundingamount.trim() === '') {
            errors.push("Funding amount is required");
            }
            
            setValidationErrors(errors);
            return errors;
        };
        
        const showConfirmationModal = () => {
            // Run validation before showing modal
            const errors = validateApplicationData();
            
            // Always show the modal
            const confirmModal = new bootstrap.Modal(document.getElementById('submit_confirmation_modal'));
            confirmModal.show();
        };

        // get capital quest response details
        const getCapitalQuestResponseDetails = async () => {
            try {

                setIsLoading(true);

                console.log("cqId", cqId);

                const requestUrl =
                    url.API_BASE_URL +
                    url.API_GET_CHALLENGE_RESPONSE_CQ_DETAILS +
                    `/${cqId}?token=${token}`;

                console.log("Capital quest response request URL:", requestUrl);

                const response = await getData(requestUrl);

                console.log("Response from capital quest API:", response);

                if (response.status) {

                    setJobPostDetails(response.data?.challengeid?.description ?? "");

                    setCapitalQuestResponseData(response.data);

                    setUserName(response.data?.candidateName);
                    setUserEmail(response.data?.candidateEmail);
                    setUserPhone(response.data?.candidatePhone );
                    setUserPosition(response.data?.candidatePosition);
                    setUserImagePath(response.data?.profilepicture?.path);
                    setUserImageId(response.data?.userImageId);

                    // set founder list
                    setFounderList(response.data?.founder);
                    setUserImageId(response.data?.userImageId);

                    // Set public/private visibility settings
                    setPublicPersonalInformation(response.data?.personalinformation);
                    setPublicExecutiveSummary(response.data?.executiveSummary);
                    setPublicFoundingTeam(response.data?.foundingTeam);
                    setPublicAboutTheProductOrService(
                        response.data?.aboutTheProductOrService
                    );
                    setPublicMarketAnalysis(response.data?.marketAnalysis);
                    setPublicTractionAndMetrics(response.data?.tractionAndMetrics);
                    setPublicBusinessModel(response.data?.businessModel);
                    setPublicRequestForFunding(response.data?.requestForFunding);
                    setPublicFinancialProjections(
                        response.data?.financialProjections
                    );
                    setPublicExitStrategy(response.data?.exitStrategy);
                    setPublicRisksAndMitigation(
                        response.data?.risksAndMitigation
                    );

                }

                setIsLoading(false);

            } catch (error) {
                console.error("Error fetching capital quest response details:", error);
            }
        }

        // function to handle the submission of the application
        const handleSubmitApplication = async () => {
            try {
            // map user data to capitalQuestResponseData
            capitalQuestResponseData.userName = userName;
            capitalQuestResponseData.userEmail = userEmail;
            capitalQuestResponseData.userPhone = userPhone;
            capitalQuestResponseData.userPosition = userPosition;
            capitalQuestResponseData.userImageId = userimageid;
            capitalQuestResponseData.founders = founderList;
        
            // Add public/private fields to the submission data
            capitalQuestResponseData.personalinformation = publicPersonalInformation;
            capitalQuestResponseData.executiveSummary = publicExecutiveSummary;
            capitalQuestResponseData.foundingTeam = publicFoundingTeam;
            capitalQuestResponseData.aboutTheProductOrService = publicAboutTheProductOrService;
            capitalQuestResponseData.marketAnalysis = publicMarketAnalysis;
            capitalQuestResponseData.tractionAndMetrics = publicTractionAndMetrics;
            capitalQuestResponseData.businessModel = publicBusinessModel;
            capitalQuestResponseData.requestForFunding = publicRequestForFunding;
            capitalQuestResponseData.financialProjections = publicFinancialProjections;
            capitalQuestResponseData.exitStrategy = publicExitStrategy;
            capitalQuestResponseData.risksAndMitigation = publicRisksAndMitigation;
        
            setSubmittingData(true);
        
            // Prepare data for submission
            const requestUrl =
                url.API_BASE_URL + 
                url.API_UPDATE_CHALLENGE_RESPONSE_CQ_DETAILS + 
                `/${cqId}?token=${token}`;
        
            const response = await postData(requestUrl, capitalQuestResponseData);
        
            console.log("Response from capital quest API:", response);
        
            setSubmittingData(false);
        
            if (response.status) {
                // Hide modal and show success message
                let modal = document.querySelector("#submit_confirmation_modal");
                let bootstrapModal = bootstrap.Modal.getInstance(modal);
                bootstrapModal.hide();
        
                // // show success modal 
                // let successModal = new bootstrap.Modal(
                // document.getElementById("apply_success_mdl")
                // );
                // successModal.show();
        
            } else {
                throw new Error('Failed to submit');
            }
            } catch (error) {
            console.error('Error submitting application:', error);
            alert("Failed to submit application. Please try again.");
            }
        };
        
        //function for image upload
        const imageUploadHandler = async (e) => {
            const file = e.target.files[0];
        
            console.log(file);
        
            try {
            const requestUrl =
                url.API_BASE_URL + url.API_SINGLE_FILE_UPLOAD + `?token=${token}`;
        
            const response = await uploadSingleFile(requestUrl, file);
        
            console.log("upload response-------->", response);
        
            if (response.status) {
                setUserImagePath(response.data.path);
                setUserImageId(response.data._id);
            }
            } catch (error) {
            console.error(error.message);
            }
        };
        
        // reset capital quest form
        const resetCapitalQuest = () => {
            
            setUserName('');
            setUserEmail('');
            setUserImagePath('');
            setUserImageId('');
            setUserPhone('');
            setUserPosition('');  
        
            // reset this form reload getChallengeDetails
            setCapitalQuestResponseData(defaultQuestData);
        
        }

        // useEffect to fetch capital quest response details
        useEffect(() => {

            console.log('challengeResponseDetails in cqPanel', challengeResponseDetails);

            if (cqId && cqId !== "") {

                console.log("cqId in cq panel", cqId);
                getCapitalQuestResponseDetails();
            }

        }, [cqId]);

        return (
            <div className="challenge-response-cq-panel">
                <div className="cq-panel-header">
                    {isLoading ? (
                    <p className="text-primary">
                        <span
                        className="mx-2 spinner-border spinner-border-sm"
                        role="status"
                        >
                        <i className="visually-hidden">Loading...</i>
                        </span>
                    </p>
                    ) : (
                    <>
                        <div className="action d-flex flex-wrap align-items-center gap-2 justify-content-end">
                        <button
                            onClick={createContactChatHandler}
                            className="btn btn-primary d-flex align-items-center"
                        >
                            <span className="d-block material-symbols-outlined icon-md text-primary fs-4 text-white">
                            chat
                            </span>
                        </button>

                        <Link
                            className="btn btn-outline-primary d-flex flex-auto"
                            target="_blank"
                            to={`/admin/customerfeedback/${params.rid}`}
                        >
                            <span className="ms-2">Customer's Feedbacks</span>
                        </Link>
                        <button
                            data-bs-toggle="modal"
                            data-bs-target="#aisuggest_modal"
                            className="btn btn-primary"
                            onClick={() => setAskAiSuggest(true)}
                        >
                            Ask AI
                        </button>
                        </div>

                        <p className="text-primary">
                        <span>
                            <b className="text-black">Code</b> :{" "}
                            {challengeResponseDetails.responsecode}{" "}
                        </span>
                        <span className="ms-4">
                            {" "}
                            <b className="text-black"> Status </b>:{" "}
                            {challengeResponseDetails.approvalstatus === "1"
                            ? "Accepted"
                            : challengeResponseDetails.approvalstatus === "2"
                            ? "Rejected"
                            : "Pending Approval"}
                        </span>

                        
                        </p>
                    </>
                    )}
                </div>
                <div className="cq-panel-content">
                    {/* <!-- ========== Capital Quest Sections ========== --> */}
                    <div className="builder_item white_shadow_bx px-4 py-0 mb-3">

                        {/* Add User Information Section with privacy toggle */}
                        <div className="build_innr_row py-4 overflow-hidden">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">

                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Personal Information <span className="text-danger">*</span></h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicPersonalInformation}
                                    onChange={(e) => setPublicPersonalInformation(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicPersonalInformation ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#submitter_info_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>

                        <div className="builder_usr_row d-flex justify-content-between">

                            <div className="build_usrleft_pnl d-flex gap-3">
                            <div className="build_usrpic position-relative">
                                <figure>
                                <img
                                    className="upload_img"
                                    src={
                                    userImagePath == ""
                                        ? assetImages.defaultUser
                                        : url.SERVER_URL + userImagePath
                                    }
                                    alt=""
                                />
                                </figure>
                                <a href="#" className="upload_camera">
                                <input
                                    type="file"
                                    className="file_btn"
                                    id="uploadedImageFile"
                                    onChange={imageUploadHandler}
                                />
                                <span className="material-symbols-outlined">photo_camera</span>
                                </a>
                            </div>

                            <div className="build_usrinfo">
                                <h3>
                                {userName || <span className="text-muted">Full Name <span className="text-danger">*</span></span>}
                                <span className="d-block">{userPosition}</span>
                                </h3>
                                <ul className="list_stye_none d-flex align-items-center mt-3 gap-4 mb-3">
                                <li className="d-flex align-items-center gap-2">
                                    <i className="material-symbols-outlined">email</i>
                                    <span>{userEmail || <span className="text-muted">Email <span className="text-danger">*</span></span>}</span>
                                </li>
                                </ul>
                                <ul className="list_stye_none d-flex align-items-center mt-3 gap-4 mb-3">
                                <li className="d-flex align-items-center gap-2">
                                    <i className="material-symbols-outlined">phone</i>
                                    <span>{userPhone || '_ _ - _ _ _ _ _'}</span>
                                </li>
                                </ul>
                            </div>
                            </div>

                        </div>
                        </div>

                        {/* Executive Summary Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Executive Summary <span className="text-danger">*</span></h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicExecutiveSummary}
                                    onChange={(e) => setPublicExecutiveSummary(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicExecutiveSummary ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#executive_summary_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>

                        <div
                            className="build_experience_details"
                            style={{ marginLeft: "30px" }}
                        >
                            <p className="mb-2">
                            <span>Startup Name: <span className="text-danger">*</span></span>{" "}
                            {capitalQuestResponseData.startupname || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Website: </span>{" "}
                            {capitalQuestResponseData.startupwebsite || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Description: <span className="text-danger">*</span></span>
                            {capitalQuestResponseData.startupdescription || "N/A"}
                            </p>

                            <p className="mb-2">
                            <span>Email: </span>{" "}
                            {capitalQuestResponseData.startupemail || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Tagline: </span>{" "}
                            {capitalQuestResponseData.startuptagline || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Problem Statement: <span className="text-danger">*</span></span>{" "}
                            {capitalQuestResponseData.problemstatement || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Solution: <span className="text-danger">*</span></span>{" "}
                            {capitalQuestResponseData.solution || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Unique Value Proposition: </span>{" "}
                            {capitalQuestResponseData.uniquevalueproposition || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Business Model: </span>{" "}
                            {capitalQuestResponseData.businessmodel || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Current Status: </span>{" "}
                            {capitalQuestResponseData.currentstatus || "N/A"}
                            </p>
                        </div>
                        </div>

                        {/* Founding Team Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Founding Team <span className="text-danger">*</span></h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicFoundingTeam}
                                    onChange={(e) => setPublicFoundingTeam(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicFoundingTeam ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#founding_team_modal"
                                onClick={() => {
                                    setFounderIndex(null);
                                    setFounderData({
                                    name: "",
                                    role: "",
                                    about: "",
                                    experience: "",
                                    skills: "",
                                    linkedin: "",
                                    equityholding: "",
                                    notableadvisors: "",
                                    });
                                }}
                                >
                                <i className="material-symbols-outlined">add</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        {founderList && founderList.length === 0 && (
                            <div className="alert alert-warning mb-3" role="alert">
                            Please add at least one founder with name and role.
                            </div>
                        )}
                        <div className="build_experience_details">
                            {founderList && founderList.map((founder, index) => (
                            <div
                                className="build_exprience_row d-flex justify-content-between align-items-start pb-4"
                                key={index}
                            >
                                <div className="left_exp_info d-flex gap-3 align-items-start">
                                <div className="exp_logo"></div>
                                <div className="exp_logo_details">
                                    <h4>
                                    {founder.name} <span>({founder.role ? founder.role : founder.position})</span>
                                    </h4>
                                    <p>About: {founder.about}</p>
                                    <p>Experience: {founder.experience}</p>
                                    <p>Skills: {founder.skills}</p>
                                    <p>LinkedIn: {founder.linkedin}</p>
                                    <p>Equity Holding: {founder.equityholding}</p>
                                    <p>Notable Advisors: {founder.notableadvisors}</p>
                                </div>
                                </div>
                                <div className="build_edit_icon">
                                <Link
                                    to="#"
                                    data-bs-toggle="modal"
                                    data-bs-target="#founding_team_modal"
                                    onClick={() => updateFounderBlock(founder, index)}
                                >
                                    <i className="material-symbols-outlined">edit</i>
                                </Link>
                                <Link to="#" onClick={() => deleteFounderBlock(index)}>
                                    <i className="material-symbols-outlined">delete</i>
                                </Link>
                                </div>
                            </div>
                            ))}
                        </div>
                        </div>

                        {/* Product/Service Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Product/Service <span className="text-danger">*</span></h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicAboutTheProductOrService}
                                    onChange={(e) => setPublicAboutTheProductOrService(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicAboutTheProductOrService ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#product_service_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Product Description: <span className="text-danger">*</span></span>
                            {capitalQuestResponseData.productdescription || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Current Development Stage: </span>
                            {capitalQuestResponseData.currentdevelopmentstage || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Development Roadmap: </span>
                            {capitalQuestResponseData.productdevelopmentroadmap || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Technology: </span>
                            {capitalQuestResponseData.technology || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Intellectual Property: </span>
                            {capitalQuestResponseData.intellectualproperty || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Competitive Advantage: </span>
                            {capitalQuestResponseData.productcompetitiveadvantage || "N/A"}
                            </p>
                        </div>
                        </div>

                        {/* Market Analysis Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Market Analysis</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicMarketAnalysis}
                                    onChange={(e) => setPublicMarketAnalysis(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicMarketAnalysis ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#market_analysis_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Market Size: </span>
                            {capitalQuestResponseData.marketsize || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Market Trends: </span>
                            {capitalQuestResponseData.markettrends || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Target Market: </span>
                            {capitalQuestResponseData.targetmarket || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Competitors: </span>
                            {capitalQuestResponseData.competitors || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Competitive Advantage: </span>
                            {capitalQuestResponseData.competitiveadvantage || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Entry Barriers: </span>
                            {capitalQuestResponseData.entrybarriers || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Differentiation Strategy: </span>
                            {capitalQuestResponseData.differentiationstrategy || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Customer Acquisition Strategy: </span>
                            {capitalQuestResponseData.customeracquisitionstrategy || "N/A"}
                            </p>
                        </div>
                        </div>

                        {/* Traction Metrics Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Traction and Metrics</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicTractionAndMetrics}
                                    onChange={(e) => setPublicTractionAndMetrics(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicTractionAndMetrics ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link to="#" data-bs-toggle="modal" data-bs-target="#traction_metrics_modal">
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Main KPIs: </span>
                            {capitalQuestResponseData.mainkpis || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Customer Evolution: </span>
                            {capitalQuestResponseData.customerevolution || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Growth Rate: </span>
                            {capitalQuestResponseData.growthrate || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Revenue: </span>
                            {capitalQuestResponseData.revenue || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>CAC and CLTV: </span>
                            {capitalQuestResponseData.cacandcltv || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Strategic Partnerships: </span>
                            {capitalQuestResponseData.stategicpartnerships || "N/A"}
                            </p>
                            {capitalQuestResponseData.customertestimonials?.length > 0 && (
                            <div className="mb-2">
                                <span>Customer Testimonials:</span>
                                <ul className="list-unstyled mt-2 ms-3">
                                {capitalQuestResponseData.customertestimonials.map((testimonial, index) => (
                                    <li key={index}>• {testimonial}</li>
                                ))}
                                </ul>
                            </div>
                            )}
                        </div>
                        </div>

                        {/* Business Model Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Business Model</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicBusinessModel}
                                    onChange={(e) => setPublicBusinessModel(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicBusinessModel ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link to="#" data-bs-toggle="modal" data-bs-target="#business_model_modal">
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Revenue Sources: </span>
                            {capitalQuestResponseData.revenuesources || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Distribution Channels: </span>
                            {capitalQuestResponseData.distrubutionchannels || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Gross Margin: </span>
                            {capitalQuestResponseData.grossmargin || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Scalability Plan: </span>
                            {capitalQuestResponseData.scalabilityplan || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Real-time Order Tracking: </span>
                            {capitalQuestResponseData.realtimeordertracking ? "Yes" : "No"}
                            </p>
                            {capitalQuestResponseData.pricingstrategy?.length > 0 && (
                            <div className="mb-2">
                                <span>Pricing Strategy:</span>
                                <ul className="list-unstyled mt-2 ms-3">
                                {capitalQuestResponseData.pricingstrategy.map((strategy, index) => (
                                    <li key={index}>• {strategy.duration} - {strategy.name} (${strategy.price})</li>
                                ))}
                                </ul>
                            </div>
                            )}
                        </div>
                        </div>

                        {/* Funding Request Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                                <div className="build_hdng d-flex align-items-center gap-1">
                                <i className="material-symbols-outlined">drag_indicator</i>
                                <h3>Funding Request <span className="text-danger">*</span></h3>
                                </div>

                                <div className="d-flex align-items-center gap-2">
                                <div>
                                    <FormControlLabel
                                    control={
                                        <Switch
                                        checked={publicRequestForFunding}
                                        onChange={(e) => setPublicRequestForFunding(e.target.checked)}
                                        color="primary"
                                        />
                                    }
                                    label={publicRequestForFunding ? 'Public' : 'Private'}
                                    />
                                </div>
                                <div className="build_edit_icon">
                                    <Link to="#" data-bs-toggle="modal" data-bs-target="#funding_request_modal">
                                    <i className="material-symbols-outlined">edit</i>
                                    </Link>
                                </div>
                                </div>
                            </div>
                            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                                <p className="mb-2">
                                <span>Funding Amount: <span className="text-danger">*</span></span>
                                {capitalQuestResponseData.fundingamount || "N/A"}
                                </p>
                                <p className="mb-2">
                                <span>Proposed Value: </span>
                                {capitalQuestResponseData.proposedvalue || "N/A"}
                                </p>
                                <p className="mb-2">
                                <span>Funding Use: </span>
                                {capitalQuestResponseData.fundinguse || "N/A"}
                                </p>
                                <p className="mb-2">
                                <span>Expected Runway: </span>
                                {capitalQuestResponseData.expectedrunway || "N/A"}
                                </p>
                                <p className="mb-2">
                                <span>Milestones: </span>
                                {capitalQuestResponseData.milestones || "N/A"}
                                </p>
                                {capitalQuestResponseData.previousfunding?.length > 0 && (
                                <div className="mb-2">
                                    <span>Previous Funding:</span>
                                    <ul className="list-unstyled mt-2 ms-3">
                                    {capitalQuestResponseData.previousfunding.map((funding, index) => (
                                        <li key={index}>• {new Date(funding.date).toLocaleDateString()} - {funding.company} (${funding.amount})</li>
                                    ))}
                                    </ul>
                                </div>
                                )}
                            </div>
                        </div>

                        {/* Financial Projections Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Financial Projections</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicFinancialProjections}
                                    onChange={(e) => setPublicFinancialProjections(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicFinancialProjections ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#financial_projections_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Revenue Projections: </span>
                            {capitalQuestResponseData.revenueprojections || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Break-even Point: </span>
                            {capitalQuestResponseData.breakevenpoint || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Main Assumptions: </span>
                            {capitalQuestResponseData.mainassumptions || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Scenarios: </span>
                            {capitalQuestResponseData.scenarios || "N/A"}
                            </p>
                        </div>
                        </div>

                        {/* Exit Strategy Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Exit Strategy</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicExitStrategy}
                                    onChange={(e) => setPublicExitStrategy(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicExitStrategy ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link to="#" data-bs-toggle="modal" data-bs-target="#exit_strategy_modal">
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Potential Acquirers: </span>
                            {capitalQuestResponseData.potentialacquirers || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Timeframe: </span>
                            {capitalQuestResponseData.timeframe || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Estimated Return: </span>
                            {capitalQuestResponseData.estimatereturn || "N/A"}
                            </p>
                            {capitalQuestResponseData.exitinsector?.length > 0 && (
                            <div className="mb-2">
                                <span>Exit Sectors: </span>
                                <ul className="list-unstyled mt-2 ms-3">
                                {capitalQuestResponseData.exitinsector.map((sector, index) => (
                                    <li key={index}>• {sector}</li>
                                ))}
                                </ul>
                            </div>
                            )}
                        </div>
                        </div>

                        {/* Risks and Mitigation Section with privacy toggle */}
                        <div className="build_innr_row py-4">
                        <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
                            <div className="build_hdng d-flex align-items-center gap-1">
                            <i className="material-symbols-outlined">drag_indicator</i>
                            <h3>Risks and Mitigation</h3>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                            <div>
                                <FormControlLabel
                                control={
                                    <Switch
                                    checked={publicRisksAndMitigation}
                                    onChange={(e) => setPublicRisksAndMitigation(e.target.checked)}
                                    color="primary"
                                    />
                                }
                                label={publicRisksAndMitigation ? 'Public' : 'Private'}
                                />
                            </div>
                            <div className="build_edit_icon">
                                <Link
                                to="#"
                                data-bs-toggle="modal"
                                data-bs-target="#risks_mitigation_modal"
                                >
                                <i className="material-symbols-outlined">edit</i>
                                </Link>
                            </div>
                            </div>
                        </div>
                        <div className="build_experience_details" style={{ marginLeft: "30px" }}>
                            <p className="mb-2">
                            <span>Business Risks: </span>
                            {capitalQuestResponseData.businessrisks || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>Mitigation Strategies: </span>
                            {capitalQuestResponseData.mitigationstrategies || "N/A"}
                            </p>
                            <p className="mb-2">
                            <span>SWOT Analysis: </span>
                            {capitalQuestResponseData.swotanalysis || "N/A"}
                            </p>
                        </div>
                        </div>

                        {/* After Risks and Mitigation Section */}
                        <div className="d-flex flex-column align-items-end mt-4 mb-4">
                        <p className="text-muted mb-2">Fields marked with <span className="text-danger">*</span> are required</p>
                        <button
                            className="btn btn-primary"
                            onClick={showConfirmationModal}
                        >
                            Submit Application
                        </button>
                        </div>

                    </div>

                    {/* Modals */}
                    <ExecutiveSummaryModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <FoundingTeamModal
                    founderIndex={founderIndex}
                    founderData={founderData}
                    setFounderData={setFounderData}
                    founderList={founderList}
                    setFounderList={setFounderList}
                    setFounderIndex={setFounderIndex}
                    />
                    <ProductServiceModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <MarketAnalysisModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <TractionMetricsModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <BusinessModelModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <FundingRequestModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <FinancialProjectionsModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <ExitStrategyModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <RisksMitigationModal
                    capitalQuestResponseData={capitalQuestResponseData}
                    setCapitalQuestResponseData={setCapitalQuestResponseData}
                    />
                    <SubmitterInfoModal
                    userName={userName}
                    setUserName={setUserName}
                    userEmail={userEmail}
                    setUserEmail={setUserEmail}
                    userPhone={userPhone}
                    setUserPhone={setUserPhone}
                    userPosition={userPosition}
                    setUserPosition={setUserPosition}
                    />

                    {/* Submit Confirmation Modal with enhanced UI */}
                    <div className="modal fade" id="submit_confirmation_modal">
                        <div className="modal-dialog modal-lg">
                            <div className="modal-content">
                                <div className="modal-header">
                                <h5 className="modal-title">
                                    {validationErrors.length > 0 ? 'Required Fields Missing' : 'Confirm Submission'}
                                </h5>
                                <button
                                    type="button"
                                    className="close"
                                    data-bs-dismiss="modal"
                                    aria-label="Close"
                                >
                                    <i className="material-symbols-outlined">close</i>
                                </button>
                                </div>
                                <div className="modal-body">
                                {/* Show validation errors if any */}
                                {validationErrors.length > 0 ? (
                                    <div className="alert alert-danger">
                                    <p><strong>Please correct the following issues before submitting:</strong></p>
                                    <ul className="mb-0">
                                        {validationErrors.map((error, index) => (
                                        <li key={index}>{error}</li>
                                        ))}
                                    </ul>
                                    </div>
                                ) : (
                                    <div className="alert alert-warning">
                                    <strong>Warning:</strong> Make sure you added all necessary information.
                                    Once submitted, it can't be edited.
                                    </div>
                                )}

                                {/* Styled submission review */}
                                <div className="submission-review">
                                    <h6 className="text-center mb-4">Review Your Information</h6>
                                    
                                    {/* Personal Information */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Personal Information <span className="text-danger">*</span></h6>
                                        <span className="ms-2 badge bg-secondary">{publicPersonalInformation ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Name:</strong> <span className="text-danger">*</span> {userName || "Not provided"}</p>
                                            <p><strong>Email:</strong> <span className="text-danger">*</span> {userEmail || "Not provided"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Phone:</strong> {userPhone || "Not provided"}</p>
                                            <p><strong>Position:</strong> {userPosition || "Not provided"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Executive Summary */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Executive Summary <span className="text-danger">*</span></h6>
                                        <span className="ms-2 badge bg-secondary">{publicExecutiveSummary ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Startup Name:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.startupname || "Not provided"}</p>
                                            <p><strong>Website:</strong> {capitalQuestResponseData.startupwebsite || "N/A"}</p>
                                            <p><strong>Email:</strong> {capitalQuestResponseData.startupemail || "N/A"}</p>
                                            <p><strong>Tagline:</strong> {capitalQuestResponseData.startuptagline || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Description:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.startupdescription ? capitalQuestResponseData.startupdescription.substring(0, 100) + '...' : "Not provided"}</p>
                                            <p><strong>Problem Statement:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.problemstatement ? capitalQuestResponseData.problemstatement.substring(0, 100) + '...' : "Not provided"}</p>
                                            <p><strong>Solution:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.solution ? capitalQuestResponseData.solution.substring(0, 100) + '...' : "Not provided"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Founding Team */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Founding Team <span className="text-danger">*</span></h6>
                                        <span className="ms-2 badge bg-secondary">{publicFoundingTeam ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        {founderList.length > 0 ? (
                                        founderList.map((founder, index) => (
                                            <div key={index} className="p-3 mb-3 border rounded">
                                            <h6>Founder {index + 1}</h6>
                                            <div className="row">
                                                <div className="col-md-6">
                                                <p><strong>Name:</strong> <span className="text-danger">*</span> {founder.name || "Not provided"}</p>
                                                <p><strong>Role:</strong> <span className="text-danger">*</span> {founder.role || "Not provided"}</p>
                                                <p><strong>LinkedIn:</strong> {founder.linkedin || "N/A"}</p>
                                                <p><strong>Equity Holding:</strong> {founder.equityholding || "N/A"}</p>
                                                </div>
                                                <div className="col-md-6">
                                                <p><strong>Experience:</strong> {founder.experience || "N/A"}</p>
                                                <p><strong>Skills:</strong> {founder.skills || "N/A"}</p>
                                                <p><strong>About:</strong> {founder.about ? founder.about.substring(0, 70) + '...' : "N/A"}</p>
                                                <p><strong>Notable Advisors:</strong> {founder.notableadvisors || "N/A"}</p>
                                                </div>
                                            </div>
                                            </div>
                                        ))
                                        ) : (
                                        <div className="alert alert-warning">
                                            <p className="mb-0">No founding team members added. At least one founder is required.</p>
                                        </div>
                                        )}
                                    </div>
                                    </div>

                                    {/* Product/Service */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Product/Service <span className="text-danger">*</span></h6>
                                        <span className="ms-2 badge bg-secondary">{publicAboutTheProductOrService ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Description:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.productdescription ? capitalQuestResponseData.productdescription.substring(0, 100) + '...' : "Not provided"}</p>
                                            <p><strong>Development Stage:</strong> {capitalQuestResponseData.currentdevelopmentstage || "N/A"}</p>
                                            <p><strong>Roadmap:</strong> {capitalQuestResponseData.productdevelopmentroadmap ? capitalQuestResponseData.productdevelopmentroadmap.substring(0, 70) + '...' : "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Technology:</strong> {capitalQuestResponseData.technology || "N/A"}</p>
                                            <p><strong>Intellectual Property:</strong> {capitalQuestResponseData.intellectualproperty || "N/A"}</p>
                                            <p><strong>Competitive Advantage:</strong> {capitalQuestResponseData.productcompetitiveadvantage || "N/A"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Market Analysis */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Market Analysis</h6>
                                        <span className="ms-2 badge bg-secondary">{publicMarketAnalysis ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Market Size:</strong> {capitalQuestResponseData.marketsize || "N/A"}</p>
                                            <p><strong>Market Trends:</strong> {capitalQuestResponseData.markettrends || "N/A"}</p>
                                            <p><strong>Target Market:</strong> {capitalQuestResponseData.targetmarket || "N/A"}</p>
                                            <p><strong>Competitors:</strong> {capitalQuestResponseData.competitors || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Competitive Advantage:</strong> {capitalQuestResponseData.competitiveadvantage || "N/A"}</p>
                                            <p><strong>Entry Barriers:</strong> {capitalQuestResponseData.entrybarriers || "N/A"}</p>
                                            <p><strong>Differentiation:</strong> {capitalQuestResponseData.differentiationstrategy || "N/A"}</p>
                                            <p><strong>Customer Acquisition:</strong> {capitalQuestResponseData.customeracquisitionstrategy || "N/A"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Funding Request */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Funding Request <span className="text-danger">*</span></h6>
                                        <span className="ms-2 badge bg-secondary">{publicRequestForFunding ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Funding Amount:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.fundingamount || "Not provided"}</p>
                                            <p><strong>Proposed Value:</strong> {capitalQuestResponseData.proposedvalue || "N/A"}</p>
                                            <p><strong>Funding Use:</strong> {capitalQuestResponseData.fundinguse || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Expected Runway:</strong> {capitalQuestResponseData.expectedrunway || "N/A"}</p>
                                            <p><strong>Milestones:</strong> {capitalQuestResponseData.milestones || "N/A"}</p>
                                            {capitalQuestResponseData.previousfunding?.length > 0 && (
                                            <div>
                                                <p><strong>Previous Funding:</strong></p>
                                                <ul className="list-unstyled ms-3">
                                                {capitalQuestResponseData.previousfunding.slice(0, 2).map((funding, index) => (
                                                    <li key={index}>• {new Date(funding.date).toLocaleDateString()} - {funding.company} (${funding.amount})</li>
                                                ))}
                                                {capitalQuestResponseData.previousfunding.length > 2 && <li>• ... and {capitalQuestResponseData.previousfunding.length - 2} more</li>}
                                                </ul>
                                            </div>
                                            )}
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Traction and Metrics Section */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Traction and Metrics</h6>
                                        <span className="ms-2 badge bg-secondary">{publicTractionAndMetrics ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Main KPIs:</strong> {capitalQuestResponseData.mainkpis || "N/A"}</p>
                                            <p><strong>Customer Evolution:</strong> {capitalQuestResponseData.customerevolution || "N/A"}</p>
                                            <p><strong>Growth Rate:</strong> {capitalQuestResponseData.growthrate || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Revenue:</strong> {capitalQuestResponseData.revenue || "N/A"}</p>
                                            <p><strong>CAC and CLTV:</strong> {capitalQuestResponseData.cacandcltv || "N/A"}</p>
                                            <p><strong>Strategic Partnerships:</strong> {capitalQuestResponseData.stategicpartnerships || "N/A"}</p>
                                        </div>
                                        </div>
                                        {capitalQuestResponseData.customertestimonials?.length > 0 && (
                                        <div>
                                            <p><strong>Customer Testimonials:</strong></p>
                                            <ul className="list-unstyled ms-3">
                                            {capitalQuestResponseData.customertestimonials.slice(0, 2).map((testimonial, index) => (
                                                <li key={index}>• {testimonial.substring(0, 70)}...</li>
                                            ))}
                                            {capitalQuestResponseData.customertestimonials.length > 2 && 
                                                <li>• ... and {capitalQuestResponseData.customertestimonials.length - 2} more</li>}
                                            </ul>
                                        </div>
                                        )}
                                    </div>
                                    </div>

                                    {/* Business Model Section */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Business Model</h6>
                                        <span className="ms-2 badge bg-secondary">{publicBusinessModel ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Revenue Sources:</strong> {capitalQuestResponseData.revenuesources || "N/A"}</p>
                                            <p><strong>Distribution Channels:</strong> {capitalQuestResponseData.distrubutionchannels || "N/A"}</p>
                                            <p><strong>Gross Margin:</strong> {capitalQuestResponseData.grossmargin || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Scalability Plan:</strong> {capitalQuestResponseData.scalabilityplan || "N/A"}</p>
                                            <p><strong>Real-time Order Tracking:</strong> {capitalQuestResponseData.realtimeordertracking ? "Yes" : "No"}</p>
                                            
                                            {capitalQuestResponseData.pricingstrategy?.length > 0 && (
                                            <div>
                                                <p><strong>Pricing Strategy:</strong></p>
                                                <ul className="list-unstyled ms-3">
                                                {capitalQuestResponseData.pricingstrategy.slice(0, 2).map((strategy, index) => (
                                                    <li key={index}>• {strategy.duration} - {strategy.name} (${strategy.price})</li>
                                                ))}
                                                {capitalQuestResponseData.pricingstrategy.length > 2 && 
                                                    <li>• ... and {capitalQuestResponseData.pricingstrategy.length - 2} more</li>}
                                                </ul>
                                            </div>
                                            )}
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Financial Projections Section */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Financial Projections</h6>
                                        <span className="ms-2 badge bg-secondary">{publicFinancialProjections ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Revenue Projections:</strong> {capitalQuestResponseData.revenueprojections || "N/A"}</p>
                                            <p><strong>Break-even Point:</strong> {capitalQuestResponseData.breakevenpoint || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>Main Assumptions:</strong> {capitalQuestResponseData.mainassumptions || "N/A"}</p>
                                            <p><strong>Scenarios:</strong> {capitalQuestResponseData.scenarios || "N/A"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Exit Strategy Section */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Exit Strategy</h6>
                                        <span className="ms-2 badge bg-secondary">{publicExitStrategy ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Potential Acquirers:</strong> {capitalQuestResponseData.potentialacquirers || "N/A"}</p>
                                            <p><strong>Timeframe:</strong> {capitalQuestResponseData.timeframe || "N/A"}</p>
                                            <p><strong>Estimated Return:</strong> {capitalQuestResponseData.estimatereturn || "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            {capitalQuestResponseData.exitinsector?.length > 0 && (
                                            <div>
                                                <p><strong>Exit Sectors:</strong></p>
                                                <ul className="list-unstyled ms-3">
                                                {capitalQuestResponseData.exitinsector.slice(0, 3).map((sector, index) => (
                                                    <li key={index}>• {sector}</li>
                                                ))}
                                                {capitalQuestResponseData.exitinsector.length > 3 && 
                                                    <li>• ... and {capitalQuestResponseData.exitinsector.length - 3} more</li>}
                                                </ul>
                                            </div>
                                            )}
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Risks and Mitigation Section */}
                                    <div className="card mb-4">
                                    <div className="card-header d-flex align-items-center bg-light">
                                        <h6 className="mb-0">Risks and Mitigation</h6>
                                        <span className="ms-2 badge bg-secondary">{publicRisksAndMitigation ? 'Public' : 'Private'}</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <p><strong>Business Risks:</strong> {capitalQuestResponseData.businessrisks ? capitalQuestResponseData.businessrisks.substring(0, 100) + '...' : "N/A"}</p>
                                            <p><strong>Mitigation Strategies:</strong> {capitalQuestResponseData.mitigationstrategies ? capitalQuestResponseData.mitigationstrategies.substring(0, 100) + '...' : "N/A"}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p><strong>SWOT Analysis:</strong> {capitalQuestResponseData.swotanalysis ? capitalQuestResponseData.swotanalysis.substring(0, 100) + '...' : "N/A"}</p>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    {/* Privacy Settings Summary */}
                                    <div className="card mb-4">
                                    <div className="card-header bg-light">
                                        <h6 className="mb-0">Privacy Settings Summary</h6>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                        <div className="col-md-6">
                                            <ul className="list-group">
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Personal Information
                                                <span className={`badge ${publicPersonalInformation ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicPersonalInformation ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Executive Summary
                                                <span className={`badge ${publicExecutiveSummary ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicExecutiveSummary ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Founding Team
                                                <span className={`badge ${publicFoundingTeam ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicFoundingTeam ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Product/Service
                                                <span className={`badge ${publicAboutTheProductOrService ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicAboutTheProductOrService ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Market Analysis
                                                <span className={`badge ${publicMarketAnalysis ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicMarketAnalysis ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            </ul>
                                        </div>
                                        <div className="col-md-6">
                                            <ul className="list-group">
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Traction & Metrics
                                                <span className={`badge ${publicTractionAndMetrics ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicTractionAndMetrics ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Business Model
                                                <span className={`badge ${publicBusinessModel ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicBusinessModel ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Funding Request
                                                <span className={`badge ${publicRequestForFunding ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicRequestForFunding ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Financial Projections
                                                <span className={`badge ${publicFinancialProjections ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicFinancialProjections ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            <li className="list-group-item d-flex justify-content-between align-items-center">
                                                Exit Strategy & Risks
                                                <span className={`badge ${publicExitStrategy || publicRisksAndMitigation ? 'bg-success' : 'bg-secondary'}`}>
                                                {publicExitStrategy || publicRisksAndMitigation ? 'Public' : 'Private'}
                                                </span>
                                            </li>
                                            </ul>
                                        </div>
                                        </div>
                                    </div>
                                    </div>

                                    <div className="text-center mt-4">
                                    <p className="text-muted"><span className="text-danger">*</span> Indicates required fields</p>
                                    </div>
                                </div>
                                </div>
                                <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    data-bs-dismiss="modal"
                                >
                                    Cancel
                                </button>
                                
                                {/* Show Submit button only if there are no errors */}
                                {validationErrors.length === 0 && (
                                    <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={handleSubmitApplication}
                                    disabled={submittingData} // optional: disables button while submitting
                                >
                                    {submittingData ? (
                                    <>
                                        <span
                                        className="spinner-border spinner-border-sm me-2"
                                        role="status"
                                        aria-hidden="true"
                                        ></span>
                                        Please wait...
                                    </>
                                    ) : (
                                    "Confirm and Submit"
                                    )}
                                </button>
                                
                                )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Thanks modal after successfull submition and response.status == true */}
                    {/* <ApplySuccessModal
                    afterModalClose={resetCapitalQuest}
                    /> */}

                    <AiSuggestModal
                            challengeResponseDetails={challengeResponseDetails}
                            aiSuggest={aiSuggest}
                            setAiSuggest={setAiSuggest}
                            applicationDetails={""}
                            jobDetails={jobPostDetails}
                            askAiSuggest={askAiSuggest}
                            setAskAiSuggest={setAskAiSuggest}
                            setReloadResponseDetails={setReloadResponseDetails}
                        />

                </div>
            </div>
        );
}

export default ChallengeResponseCqPanel;