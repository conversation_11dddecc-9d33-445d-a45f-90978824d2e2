/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams, useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Select from "react-select";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { postData, getData, putData } from "utils/Gateway";

import { assetImages } from "constants";
import AlertNotification from "components/Common/AlertNotification/AlertNotification";
import SaveCategoryHeader from "../Header/SaveCategoryHeader";
import SuccessModal from "../Modal/SuccessModal";
import { getAllCategories } from "helper/CategoryHelper/CategoryHelper";

const SaveCategoryBody = () => {
  const { id } = useParams();
  const history = useHistory();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const ecosystemSlug = localStorage.getItem("ecosystemslug");
  const { t, i18n } = useTranslation(); //for translation

  const [parentbaseCategories, setParentBaseCategories] = useState([]);
  const [levelTwoCategories, setLevelTwoCategories] = useState([]);
  const [levelThreeCategories, setLevelThreeCategories] = useState([]);
  const [levelFourCategories, setLevelFourCategories] = useState([]);
  const [levelFiveCategories, setLevelFiveCategories] = useState([]);
  const [levelSixCategories, setLevelSixCategories] = useState([]);

  const [selectedBaseCategoryId, setselectedBaseCategoryId] = useState(null);
  const [selectedLevelTwoCategoryId, setselectedLevelTwoCategoryId] =
    useState(null);
  const [selectedLevelThreeCategoryId, setselectedLevelThreeCategoryId] =
    useState(null);
  const [selectedLevelFourCategoryId, setselectedLevelFourCategoryId] =
    useState(null);
  const [selectedLevelFiveCategoryId, setselectedLevelFiveCategoryId] =
    useState(null);
  const [selectedLevelSixCategoryId, setselectedLevelSixCategoryId] =
    useState(null);

  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [isRoot, setIsRoot] = useState(false);
  const [parentId, setParentId] = useState(null);
  const [isFeatured, setIsFeatured] = useState(false);

  const [isSaving, setIsSaving] = useState(false);
  const [savedId, setSavedId] = useState(null);

  //alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  /* hook for validation */
  const [validation, setValidation] = useState({
    nameWarning: false,
    slugWarning: false,
    parentwarning: false,
  });

  //function for get all tags
  const getAllCategoryList = async (
    isRoot = true,
    parentId = null,
    parentColumnSlug = ""
  ) => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_CATEGORIES +
        `?ecosystemslug=${ecosystemSlug}`;

      if (isRoot) {
        requestUrl = requestUrl + `&filterisroot=${true}`;
      }

      if (parentId) {
        requestUrl = requestUrl + `&filterparents=${parentId}`;
      }

      const response = await getData(requestUrl);

      console.log(response);

      if (response.status) {
        if (isRoot && parentColumnSlug === "") {
          setParentBaseCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        } else if (isRoot == false && parentColumnSlug === "BASE") {
          setLevelTwoCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        } else if (isRoot == false && parentColumnSlug === "LEVEL2") {
          setLevelThreeCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        } else if (isRoot == false && parentColumnSlug === "LEVEL3") {
          setLevelFourCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        } else if (isRoot == false && parentColumnSlug === "LEVEL4") {
          setLevelFiveCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        } else if (isRoot == false && parentColumnSlug === "LEVEL5") {
          setLevelSixCategories(
            response.data.sort(
              (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
            )
          );
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const parentSelectionHandler = (item) => {
    setLevelTwoCategories([]);
    setLevelThreeCategories([]);
    setLevelFourCategories([]);
    setLevelFiveCategories([]);
    setLevelSixCategories([]);
    setselectedBaseCategoryId(item._id);
    setselectedLevelTwoCategoryId(null);
    setselectedLevelThreeCategoryId(null);
    setselectedLevelFourCategoryId(null);
    setselectedLevelFiveCategoryId(null);
    setselectedLevelSixCategoryId(null);
    getAllCategoryList(false, item._id, "BASE");
    setParentId(item._id);
  };

  const childOneSelectionHandler = (item) => {
    setLevelThreeCategories([]);
    setLevelFourCategories([]);
    setLevelFiveCategories([]);
    setLevelSixCategories([]);
    setselectedLevelTwoCategoryId(item._id);
    setselectedLevelThreeCategoryId(null);
    setselectedLevelFourCategoryId(null);
    setselectedLevelFiveCategoryId(null);
    setselectedLevelSixCategoryId(null);
    getAllCategoryList(false, item._id, "LEVEL2");
    setParentId(item._id);
  };

  const childTwoSelectionHandler = (item) => {
    setLevelFourCategories([]);
    setLevelFiveCategories([]);
    setLevelSixCategories([]);
    setselectedLevelThreeCategoryId(item._id);
    setselectedLevelFourCategoryId(null);
    setselectedLevelFiveCategoryId(null);
    setselectedLevelSixCategoryId(null);
    getAllCategoryList(false, item._id, "LEVEL3");
    setParentId(item._id);
  };

  const childThreeSelectionHandler = (item) => {
    setLevelFiveCategories([]);
    setLevelSixCategories([]);
    setselectedLevelFourCategoryId(item._id);
    setselectedLevelFiveCategoryId(null);
    setselectedLevelSixCategoryId(null);
    getAllCategoryList(false, item._id, "LEVEL4");
    setParentId(item._id);
  };

  const childFourSelectionHandler = (item) => {
    setLevelSixCategories([]);
    setselectedLevelFiveCategoryId(item._id);
    setselectedLevelSixCategoryId(null);
    getAllCategoryList(false, item._id, "LEVEL5");
    setParentId(item._id);
  };

  const childFiveSelectionHandler = (item) => {
    setselectedLevelSixCategoryId(item._id);
    setParentId(item._id);
  };

  const CategoryColumn = ({
    items = {},
    selected,
    onSelect,
    isActive,
    level = "",
  }) => (
    <div className="col-lg border-0 border-bottom border-bottom-lg-0 border-lg-end border-solid border-gray-300">
      <h4 className="mt-3 text-black fw-semibold">{level}</h4>
      {items && (
        <ul className="fs-md fw-semibold pb-3">
          {items.map((item, index) => (
            <li key={index}>
              <Link
                to="#"
                className={`text-gray py-2 d-flex align-items-center gap-3 justify-content-between ${
                  selected === item._id ? "text-primary" : ""
                }`}
                onClick={() => onSelect(item)}
              >
                <span className="d-block">{item.name}</span>
                <span className="d-block material-symbols-outlined">
                  chevron_right
                </span>
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );

  //reset all parent categories
  const resetAlllevelHandler = () => {
    setLevelTwoCategories([]);
    setLevelThreeCategories([]);
    setLevelFourCategories([]);
    setLevelFiveCategories([]);
    setLevelSixCategories([]);
    setselectedBaseCategoryId(null);
    setselectedLevelTwoCategoryId(null);
    setselectedLevelThreeCategoryId(null);
    setselectedLevelFourCategoryId(null);
    setselectedLevelFiveCategoryId(null);
    setselectedLevelSixCategoryId(null);

    setParentId(null);
  };

  //get details
  const getCategoryDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_CATEGORY_DETAILS +
        `/${id}` +
        `?token=${token}`;

      const response = await getData(requestUrl);

      // console.log(response);

      if (response.status) {
        setName(response.data.name);
        setSlug(response.data.slug);
        setIsRoot(response.data.isroot);
        setIsFeatured(response.data.isfeatured);
        setParentId(response.data.parent);

        if (response.data.parentids && response.data.parentids.length > 0) {
          const handlers = [
            parentSelectionHandler,
            childOneSelectionHandler,
            childTwoSelectionHandler,
            childThreeSelectionHandler,
            childFourSelectionHandler,
            childFiveSelectionHandler,
          ];

          const processCategoryLevel = async (slug, level = 0) => {
            if (level >= handlers.length) return;

            const categories = await getAllCategories(ecosystemSlug, slug);
            if (!categories.length) return;

            const matching = categories.find((cat) =>
              response.data.parentids.includes(cat._id)
            );
            if (!matching) return;

            handlers[level](matching);
            await processCategoryLevel(matching.slug, level + 1);
          };

          let catRequestUrl =
            url.API_BASE_URL +
            url.API_GET_CATEGORIES +
            `?ecosystemslug=${ecosystemSlug}&filterisroot=${true}`;

          const baseResponse = await getData(catRequestUrl);

          const matchingBase = baseResponse.data.find((cat) =>
            response.data.parentids.includes(cat._id)
          );

          if (matchingBase) {
            parentSelectionHandler(matchingBase);
            await processCategoryLevel(matchingBase.slug, 1);
          }
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  /* function to validate form */
  const validationHandler = () => {
    let isValid = true;

    if (name === "") {
      setValidation((prevState) => ({ ...prevState, nameWarning: true }));
      isValid = false;
    }

    if (slug === "") {
      setValidation((prevState) => ({ ...prevState, slugWarning: true }));
      isValid = false;
    }

    if (isRoot === false && parentId === null) {
      setValidation((prevState) => ({ ...prevState, parentwarning: true }));
      isValid = false;
    }

    return isValid;
  };

  //function for create new tag
  const saveCategoryHandler = async () => {
    if (validationHandler()) {
      setIsSaving(true);
      try {
        let categoryData = {
          name,
          slug,
          isroot: isRoot,
          parent: parentId,
          isfeatured: isFeatured,
        };

        console.log("categoryData------>", categoryData);

        let requestUrl = url.API_BASE_URL;

        let response = {};

        if (id) {
          requestUrl =
            requestUrl + url.API_UPDATE_CATEGORY + `/${id}` + `?token=${token}`;

          response = await putData(requestUrl, categoryData);
        } else {
          requestUrl = requestUrl + url.API_ADD_CATEGORY + `?token=${token}`;

          response = await postData(requestUrl, categoryData);
        }

        setSavedId(response.data._id);

        setIsSaving(false);

        console.log(response);

        if (response.status) {
          const bootstrapModal = new bootstrap.Modal(
            document.getElementById("saveSuccessModal")
          );
          bootstrapModal.show();
        }
      } catch (error) {
        console.log(error.message);
      }
    }
  };

  //function for aftermodal close
  const afterSaveModalClose = (pageName) => {
    if (pageName == "details") {
      history.push(`/admin/administration/categories/save/${savedId}`);
    } else if (pageName == "list") {
      history.push("/admin/administration/categories/list");
    }
    // else {
    //   history.push("/admin/administration/categories/save");
    // }
  };

  useEffect(() => {
    if (id) {
      getCategoryDetails();
    }
  }, [id]);

  useEffect(() => {
    getAllCategoryList();
  }, []);

  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  if (userInfo.role.slug === "ADMIN" || userInfo.role.slug === "SUPER_ADMIN") {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper bg-white pb-5">
          <div className="container-fluid px-lg-5">
            <SaveCategoryHeader reloadLabelList={resetAlllevelHandler} />

            <div className="create-lead-container p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm mt-2">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="fields-container flex-fill">
                  {/* name  */}
                  <div className="form-group mb-4">
                    <label
                      htmlFor="labelName"
                      className="d-block fs-sm fw-semibold mb-2"
                    >
                      {t("Category Name")}
                    </label>
                    <input
                      type="text"
                      id="labelName"
                      className="form-control fs-sm shadow-none"
                      placeholder={t("Enter here")}
                      value={name}
                      onChange={(e) => {
                        setName(e.target.value);
                        setValidation((prevState) => ({
                          ...prevState,
                          nameWarning: false,
                        }));
                      }}
                    />
                    {/* level warning */}
                    {validation.nameWarning && (
                      <div className="error-message mt-2">
                        <p className="d-flex align-items-center gap-1 text-danger">
                          <span className="material-symbols-outlined">
                            warning
                          </span>
                          <span>{t("Please enter label")}!</span>
                        </p>
                      </div>
                    )}
                  </div>

                  {/* slug */}
                  <div className="form-group mb-4">
                    <label
                      htmlFor="slugID"
                      className="d-block fs-sm fw-semibold mb-2"
                    >
                      {t("Slug")}
                    </label>
                    <input
                      type="text"
                      id="slug"
                      className="form-control fs-sm shadow-none"
                      placeholder={t("Enter here")}
                      value={slug}
                      onChange={(e) => {
                        setSlug(e.target.value);
                        setValidation((prevState) => ({
                          ...prevState,
                          slugWarning: false,
                        }));
                      }}
                    />
                    {/* slug warning */}
                    {validation.slugWarning && (
                      <div className="error-message mt-2">
                        <p className="d-flex align-items-center gap-1 text-danger">
                          <span className="material-symbols-outlined">
                            warning
                          </span>
                          <span>{t("Please enter slug")}!</span>
                        </p>
                      </div>
                    )}
                  </div>

                  {/* is root */}
                  <div className="form-check d-flex align-items-center gap-2 fs-sm p-0 m-0 mb-4">
                    <input
                      className="form-check-input bg-transparent border-2 border-gray-600 shadow-none m-0"
                      type="checkbox"
                      checked={isRoot}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setValidation((prevState) => ({
                            ...prevState,
                            parentwarning: false,
                          }));
                          resetAlllevelHandler();
                        }
                        setIsRoot(e.target.checked);
                      }}
                    />
                    <label className="form-check-label" htmlFor="private">
                      {t("Is Root")}
                    </label>
                  </div>

                  {/* is featured */}
                  <div className="form-check d-flex align-items-center gap-2 fs-sm p-0 m-0 mb-4">
                    <input
                      className="form-check-input bg-transparent border-2 border-gray-600 shadow-none m-0"
                      type="checkbox"
                      checked={isFeatured}
                      onChange={(e) => setIsFeatured(e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="private">
                      {t("Is Featured")}
                    </label>
                  </div>

                  {/* parent category  */}
                  <div
                    className={
                      isRoot
                        ? "d-none"
                        : "categories-container px-4 border border-gray-300 rounded-10 shadow-sm mb-3"
                    }
                  >
                    <div className="row gx-5">
                      {/* Parent Categories */}
                      <CategoryColumn
                        items={parentbaseCategories}
                        onSelect={parentSelectionHandler}
                        selected={selectedBaseCategoryId}
                        level="Base Categories"
                      />

                      {/* Child1 Categories */}
                      <CategoryColumn
                        items={levelTwoCategories}
                        onSelect={childOneSelectionHandler}
                        selected={selectedLevelTwoCategoryId}
                        level="level 2"
                      />

                      {/* Child2 Categories */}
                      <CategoryColumn
                        items={levelThreeCategories}
                        onSelect={childTwoSelectionHandler}
                        selected={selectedLevelThreeCategoryId}
                        level="level 3"
                      />

                      {/* Child3 Categories */}
                      <CategoryColumn
                        items={levelFourCategories}
                        onSelect={childThreeSelectionHandler}
                        selected={selectedLevelFourCategoryId}
                        level="level 4"
                      />

                      {/* Child4 Categories */}
                      <CategoryColumn
                        items={levelFiveCategories}
                        onSelect={childFourSelectionHandler}
                        selected={selectedLevelFiveCategoryId}
                        level="level 5"
                      />

                      {/* Child5 Categories */}
                      <CategoryColumn
                        items={levelSixCategories}
                        onSelect={childFiveSelectionHandler}
                        selected={selectedLevelSixCategoryId}
                        level="level 6"
                      />
                    </div>
                  </div>
                </div>

                <div className="action d-flex justify-content-between gap-3">
                  <Link
                    to="/admin/administration/categories/list"
                    className="btn btn-outline-primary"
                    // data-bs-dismiss="offcanvas"
                    // aria-label="Close"
                  >
                    {t("Back")}
                  </Link>
                  <button
                    type="button"
                    className="btn btn-primary"
                    disabled={isSaving ? true : false}
                    style={{
                      cursor: isSaving ? "not-allowed" : "pointer",
                    }}
                    onClick={saveCategoryHandler}
                  >
                    {t("Save")}
                    {isSaving && (
                      <div
                        className="mx-2 spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {showAlert && (
            <AlertNotification
              showAlert={showAlert}
              message={alertMessage}
              alertType={messageType}
              onClose={onAlertClose}
            />
          )}

          <SuccessModal afterSaveModalClose={afterSaveModalClose} />
        </section>
      </div>
    );
  } else {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper bg-white pb-5">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-lg text-gray fw-semibold mb-4">
                {t("Sorry....! You don't have privilege to see this content")}
              </p>
            </div>
          </div>
        </section>
      </div>
    );
  }
};

export default SaveCategoryBody;
