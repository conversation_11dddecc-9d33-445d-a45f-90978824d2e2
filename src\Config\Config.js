export const ecosystemSlug = localStorage.getItem("ecosystemslug") || "LOBEES";

export const prjectManageSlug = "PROJECT_ROLE_MANAGER";

export const invoiceOwnerEmail = "<EMAIL>";

export const invoiceBillingAddress = {
  address: "Calle de Embajadores, 9, 28012, Madrid, España",
  supportmail: "<EMAIL>",
  phone: "(+34) 615 43 94 22",
  website: "https://club.elpavonteatro.es/",
  logourl:
    "https://club.elpavonteatro.es/wp-content/uploads/2024/08/TeatroPavon-RGB-N-1.png",
};

export const joditEditorConfig = (editorHeight = null) => {
  return {
    readonly: false,
    placeholder: "",
    minHeight: editorHeight || 400,
    uploader: {
      insertImageAsBase64URI: false,
    },
    defaultActionOnPaste: "insert_as_html",
    defaultLineHeight: 1.5,
    enter: "div",
    statusbar: false,
    sizeLG: 900,
    sizeMD: 700,
    sizeSM: 400,
    toolbarAdaptive: false,
    disablePlugins: [
      "image",
      "video",
      "about",
      "classSpan",
      "file",
      "speechRecognize",
      "copyformat",
      "spellcheck",
    ],
  };
};

export const ckEditorConfig = (placeholderText = "") => {
  return {
    toolbar: [
      "bold",
      "italic",
      "|",
      "bulletedList",
      "numberedList",
      "|",
      "outdent",
      "indent",
      "|",
      "undo",
      "redo",
    ],
    placeholder: placeholderText,
    shouldNotGroupWhenFull: false,
  };
};

export const gptPromtSlug = "INVOICE_DOC";

export const reactSelectStyle = (
  padding = "0px",
  borderRadius = "0px",
  bgRequired = false,
  showBorder = true
) => {
  return {
    control: (styles, { isFocused }) => ({
      ...styles,
      backgroundColor: bgRequired ? "#ffffff" : "#ffffff", // Control background
      border: showBorder ? "1px solid #EE523C" : "1px solid #D4D4D4",
      borderRadius, // Border radius
      padding,
      boxShadow: "0 !important",
      "&:hover": {}, // Focus shadow effect
    }),
  };
};

export const filterAdvanceSelectStyle = () => {
  return {
    control: (styles, { isFocused }) => ({
      ...styles,
      backgroundColor: "inherit", // Control background
      border: "1px solid #4c4c4c",
      borderRadius: "5px", // Border radius
      padding: "3px",
      boxShadow: "0 !important",
      "&:hover": {}, // Focus shadow effect
    }),
  };
};

export const customKanbanSelectProcessStyle = {
  // options style
  option: (provided, state) => ({
    ...provided,
    color: "#666666",
    fontSize: "0.875rem",
    fontWeight: "normal",
    display: "block",
    minHeight: "1.2em",
    // whiteSpace: "nowrap",
  }),

  // Value style
  control: (styles, state) => ({
    ...styles,
    fontSize: "0.875rem",
    borderRadius: "0.5rem",
    minWidth: 296,
    border: state.isFocused ? "1px solid #D4D4D4" : "1px solid #D4D4D4",
    // This line disable the blue border
    boxShadow: state.isFocused ? 0 : 0,
    "&:hover": {
      border: state.isFocused ? "1px solid #D4D4D4" : "1px solid #D4D4D4",
    },
  }),

  // placeholder style
  placeholder: (defaultStyles) => {
    return {
      ...defaultStyles,
      fontSize: "0.875rem",
    };
  },

  // Indicator style
  // dropdownIndicator: (base, state) => {
  //   let changes = { color: "#505050" };
  //   return Object.assign(base, changes);
  // },
};
