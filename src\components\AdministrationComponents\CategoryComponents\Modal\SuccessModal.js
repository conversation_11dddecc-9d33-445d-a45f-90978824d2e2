/* eslint-disable */
import React from "react";
import { <PERSON> } from "react-router-dom";
import { assetImages } from "constants";

const SuccessModal = ({ afterSaveModalClose = () => {} }) => {
  return (
    <div
      className="modal fade"
      id="saveSuccessModal"
      tabIndex="-1"
      aria-labelledby="afterSaveLeadFromDocModal"
      aria-hidden="true"
      data-bs-backdrop="static"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content bg-white border-0 rounded-15">
          <div className="modal-body text-center p-5">
            <img
              src={assetImages.congratulationsIcon}
              alt="You have saved a lead successfuly."
              className="img-fluid mb-2"
            />
            <h2 className="mb-2">Congratulations</h2>
            <p className="fs-lg fw-semibold mb-5">
              You have saved a category successfuly.
            </p>
            <div className="action d-flex justify-content-center gap-3">
              <Link
                to="#"
                data-bs-dismiss="modal"
                className="btn btn-outline-primary"
                onClick={() => {
                  afterSaveModalClose("list");
                }}
              >
                Go to List
              </Link>
              <Link
                to="#"
                data-bs-dismiss="modal"
                className="btn btn-primary"
                onClick={() => {
                  afterSaveModalClose("details");
                }}
              >
                Update Information
              </Link>
              {/* <Link
                to="#"
                data-bs-dismiss="modal"
                className="btn btn-primary"
                onClick={() => {
                  afterSaveModalClose("new");
                }}
              >
                Add new
              </Link> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
