import React, { useEffect, useState, useRef } from "react";
import * as XLSX from "xlsx";

const CsvCustomUploadModal = () => {
  const fileInputRef = useRef(null);

  const leadFields = {
    name: { required: true, label: "Lead Name" },
    email: { required: true, label: "Email Address" },
    phone: { required: true, label: "Phone Number" },
    company: { required: true, label: "Company Name" },
    title: { required: false, label: "Job Title" },
    department: { required: false, label: "Department" },
    industry: { required: false, label: "Industry" },
    leadSource: { required: false, label: "Lead Source" },
    address: { required: false, label: "Street Address" },
    city: { required: false, label: "City" },
    state: { required: false, label: "State/Province" },
    country: { required: false, label: "Country" },
    zipCode: { required: false, label: "ZIP/Postal Code" },
    website: { required: false, label: "Website" },
    companySize: { required: false, label: "Company Size" },
    annualRevenue: { required: false, label: "Annual Revenue" },
    leadScore: { required: false, label: "Lead Score" },
    leadStatus: { required: false, label: "Lead Status" },
    priority: { required: false, label: "Priority Level" },
    linkedIn: { required: false, label: "LinkedIn Profile" },
    twitter: { required: false, label: "Twitter Handle" },
    notes: { required: false, label: "Notes" },
    utmSource: { required: false, label: "UTM Source" },
    utmMedium: { required: false, label: "UTM Medium" },
    utmCampaign: { required: false, label: "UTM Campaign" },
  };

  const [fileData, setFileData] = useState(null);
  const [headers, setHeaders] = useState([]);
  const [mapping, setMapping] = useState({});
  const [validationError, setValidationError] = useState("");
  const [invalidFields, setInvalidFields] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadStats, setUploadStats] = useState({
    leadsCreated: 0,
    leadsUpdated: 0,
    leadsSkipped: 0,
    totalProcessed: 0,
    errors: [],
  });

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    setUploadStats({
      leadsCreated: 0,
      leadsUpdated: 0,
      leadsSkipped: 0,
      totalProcessed: 0,
      errors: [],
    });
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheet = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {
          header: 1,
        });
        if (sheet.length > 1) {
          const cleanHeaders = sheet[0]
            .map((header) => (header ? header.toString().trim() : ""))
            .filter(Boolean);
          setHeaders(cleanHeaders);
          setFileData(sheet.slice(1).filter((row) => row.some((cell) => cell)));
          setValidationError("");
        } else {
          setValidationError("The file appears to be empty or invalid.");
        }
      } catch (error) {
        setValidationError(
          "Error reading file. Please ensure it's a valid Excel or CSV file."
        );
        console.error("File reading error:", error);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const handleMappingChange = (field, headerValue) => {
    setMapping((prev) => ({ ...prev, [field]: headerValue || undefined }));
  };

  const validateMapping = () => {
    const requiredFields = Object.keys(leadFields).filter(
      (field) => leadFields[field].required
    );
    const unmappedRequired = requiredFields.filter((field) => !mapping[field]);
    if (unmappedRequired.length > 0) {
      setInvalidFields(unmappedRequired);
      setValidationError(
        `Please map these required fields: ${unmappedRequired
          .map((f) => leadFields[f].label)
          .join(", ")}`
      );
      return false;
    }
    setInvalidFields([]);
    setValidationError("");
    return true;
  };

  const simulateApiCall = async (batchData) => {
    await new Promise((resolve) =>
      setTimeout(resolve, 1000 + Math.random() * 1000)
    );
    const created = Math.floor(batchData.length * 0.7);
    const updated = Math.floor(batchData.length * 0.2);
    const skipped = batchData.length - created - updated;
    return {
      status: true,
      data: {
        leadsCreated: created,
        leadsUpdated: updated,
        leadsSkipped: skipped,
        errors:
          skipped > 0
            ? [`${skipped} leads skipped due to validation errors`]
            : [],
      },
    };
  };

  const handleSubmit = async () => {
    if (!validateMapping()) return;
    setIsLoading(true);
    setUploadStats({
      leadsCreated: 0,
      leadsUpdated: 0,
      leadsSkipped: 0,
      totalProcessed: 0,
      errors: [],
    });
    try {
      const batchSize = 50;
      const totalBatches = Math.ceil(fileData.length / batchSize);
      let allErrors = [];

      for (let i = 0; i < totalBatches; i++) {
        const batchData = fileData.slice(i * batchSize, (i + 1) * batchSize);
        const response = await simulateApiCall(batchData);
        if (response.status) {
          setUploadStats((prev) => ({
            leadsCreated: prev.leadsCreated + response.data.leadsCreated,
            leadsUpdated: prev.leadsUpdated + response.data.leadsUpdated,
            leadsSkipped: prev.leadsSkipped + response.data.leadsSkipped,
            totalProcessed: prev.totalProcessed + batchData.length,
            errors: [...prev.errors, ...response.data.errors],
          }));
        } else {
          allErrors.push(`Batch ${i + 1} failed: ${response.message}`);
        }
      }
      if (allErrors.length > 0)
        setValidationError("Some batches encountered errors.");
    } catch (error) {
      console.error("Upload error:", error);
      setValidationError("An unexpected error occurred during upload.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setFileData(null);
    setHeaders([]);
    setMapping({});
    setValidationError("");
    setInvalidFields([]);
    setUploadStats({
      leadsCreated: 0,
      leadsUpdated: 0,
      leadsSkipped: 0,
      totalProcessed: 0,
      errors: [],
    });
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const getSampleValue = (field) => {
    if (!mapping[field] || !fileData || fileData.length === 0) return "";
    const headerIndex = headers.indexOf(mapping[field]);
    if (headerIndex === -1) return "";
    for (let row of fileData) {
      if (row[headerIndex] && row[headerIndex].toString().trim())
        return row[headerIndex].toString().trim();
    }
    return "";
  };

  return (
    <div
      className="modal fade"
      id="csvUploadModal"
      tabIndex="-1"
      aria-labelledby="csvUploadModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="csvUploadModalLabel">
              Upload Leads from Excel/CSV
            </h5>
            <button
              type="button"
              className="btn-close"
              onClick={handleCloseModal}
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div className="modal-body">
            <div className="row mb-3">
              <div className="col-md-6">
                <label htmlFor="fileInput" className="form-label">
                  Choose File
                </label>
                <input
                  type="file"
                  className="form-control"
                  id="fileInput"
                  ref={fileInputRef}
                  accept=".csv,.xls,.xlsx"
                  onChange={handleFileUpload}
                />
              </div>
              <div className="col-md-6">
                {fileData && (
                  <>
                    <label className="form-label">
                      File Loaded Successfully
                    </label>
                    <div className="text text-primary d-flex align-items-center">
                      <i className="bi bi-check-circle me-2"></i>
                      Rows Found to be uploaded: {fileData.length}
                    </div>
                  </>
                )}
              </div>
            </div>

            {headers.length > 0 && (
              <div className="mt-4">
                <h5 className="border-bottom pb-2 mb-4">
                  Map Excel Columns to Lead Fields
                </h5>
                <div className="row mb-2">
                  <div className="col-md-4">
                    <strong>Field Name</strong>
                  </div>
                  <div className="col-md-4">
                    <strong>Excel Column</strong>
                  </div>
                  <div className="col-md-4">
                    <strong>Sample Value</strong>
                  </div>
                </div>
                {Object.entries(leadFields).map(([field, config]) => (
                  <div className="row mb-2" key={field}>
                    <div className="col-md-4">
                      <label>
                        {config.label}{" "}
                        {config.required && (
                          <span className="text-danger">*</span>
                        )}
                      </label>
                    </div>
                    <div className="col-md-4">
                      <select
                        className={`form-select ${
                          invalidFields.includes(field) ? "is-invalid" : ""
                        }`}
                        value={mapping[field] || ""}
                        onChange={(e) =>
                          handleMappingChange(field, e.target.value)
                        }
                      >
                        <option value="">Select column...</option>
                        {headers.map((header, idx) => (
                          <option key={idx} value={header}>
                            {header}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="col-md-4">
                      <small>{getSampleValue(field) || "—"}</small>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {validationError && (
              <div className="alert alert-danger mt-4 d-flex align-items-center">
                <i className="bi bi-exclamation-triangle me-2"></i>
                {validationError}
              </div>
            )}

            {(uploadStats.totalProcessed > 0 || isLoading) && (
              <div className="border p-2 mt-4">
                <h6>Upload Summary</h6>
                <div className="row">
                  <div className="col-md-3">
                    <strong>Created:</strong> {uploadStats.leadsCreated}
                  </div>
                  <div className="col-md-3">
                    <strong>Updated:</strong> {uploadStats.leadsUpdated}
                  </div>
                  <div className="col-md-3">
                    <strong>Skipped:</strong> {uploadStats.leadsSkipped}
                  </div>
                  <div className="col-md-3">
                    <strong>Total:</strong> {uploadStats.totalProcessed}
                  </div>
                </div>
                {uploadStats.errors.length > 0 && (
                  <div className="mt-2">
                    <h6>Errors:</h6>
                    <ul>
                      {uploadStats.errors.slice(0, 5).map((err, i) => (
                        <li key={i}>{err}</li>
                      ))}
                      {uploadStats.errors.length > 5 && (
                        <li>... and {uploadStats.errors.length - 5} more</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleCloseModal}
            >
              Cancel
            </button>
            {Object.keys(mapping).length > 0 && (
              <button
                type="button"
                className="btn btn-primary"
                onClick={handleSubmit}
                disabled={isLoading || !fileData}
              >
                {isLoading ? (
                  <span
                    className="spinner-border spinner-border-sm"
                    role="status"
                    aria-hidden="true"
                  ></span>
                ) : (
                  "Upload Leads"
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CsvCustomUploadModal;
