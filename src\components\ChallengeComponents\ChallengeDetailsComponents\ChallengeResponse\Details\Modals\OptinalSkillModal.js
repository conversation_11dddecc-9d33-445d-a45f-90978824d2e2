/* eslint-disable */
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import Select from "react-select";

const OptinalSkillModal = ({
  skillList,
  userOptionalSkills,
  setuserOptionalSkills,
}) => {
  const [skillValue, setSkillValue] = useState(null);
  const [selectedSkills, setSelectedSkills] = useState([]);

  const skillSelectionHandler = (skill, index) => {
    // Check if skill is already selected
    // const isSkillSelected = selectedSkills.includes(skill);

    // if (isSkillSelected) {
    //   // If selected, remove from selectedSkills
    //   setSelectedSkills(selectedSkills.filter((skill) => skill !== skill));
    // } else {
    //   // If not selected, add to selectedSkills
    //   setSelectedSkills([...selectedSkills, skill]);
    // }
    if (skill) {
      setSkillValue(skill);
      setSelectedSkills(skill.map((item) => item));
    } else {
      setSkillValue(null);
      setSelectedSkills([]);
    }
  };

  const saveInfoHandler = () => {
    if (selectedSkills.length > 0) {
      setuserOptionalSkills(selectedSkills);
    } else {
      setuserOptionalSkills([]);
    }

    resetHandler();

    let loginModal = document.querySelector("#soft_skill_modal");
    let modal = bootstrap.Modal.getInstance(loginModal);
    modal.hide();
  };

  const resetHandler = () => {
    setSkillValue(null);
    setSelectedSkills([]);
  };

  useEffect(() => {
    if (userOptionalSkills.length > 0) {
      skillSelectionHandler(userOptionalSkills);
    }
  }, [userOptionalSkills]);

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade show" id="soft_skill_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Optional Skills</h3>
                <h5>Range of knowledge</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <i className="material-icons-outlined">close </i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="multiple_select_area">
                    <div className="option_btns mt-2">
                      {/* <div className="multiple_slct_bx"></div> */}
                      <div className="mb-4">
                        <Select
                          placeholder="Select skills"
                          isMulti
                          options={skillList}
                          value={skillValue}
                          onChange={(val) => {
                            skillSelectionHandler(val);
                          }}
                        />
                      </div>
                      <ul className="list_stye_none d-flex flex-wrap align-items-center gap-2">
                        {skillList.map((skill, index) => {
                          const isSelected = selectedSkills.includes(skill); // Check if skill is selected

                          return (
                            <li key={index}>
                              <Link
                                to="#"
                                className={`d-flex align-items-center justify-content-center ${
                                  isSelected ? "option_btns_active" : "" // Apply option_btns_active class if skill is selected
                                }`}
                                // onClick={() => {
                                //   skillSelectionHandler(skill);
                                // }}
                              >
                                {skill.label}
                              </Link>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OptinalSkillModal;
