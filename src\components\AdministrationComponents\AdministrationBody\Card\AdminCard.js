import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Card,
  CardContent,
  Typography,
  Grid,
  CardActions,
  Button,
} from "@mui/material";

const AdminCard = ({ card }) => {
  const { t } = useTranslation();

  return (
    <Grid item xs={12} sm={6} md={3}>
      <Card elevation={4} className="h-100">
        <CardContent>
          <Typography variant="h5" component="h2">
            {t(card.title)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t(card.description)}
          </Typography>
        </CardContent>
        <CardActions>
          <Button
            size="small"
            component={Link}
            to={card.link}
            className="fw-bold text-primary"
          >
            {t(card.linkText)}
          </Button>
        </CardActions>
      </Card>
    </Grid>
  );
};

export default AdminCard;
