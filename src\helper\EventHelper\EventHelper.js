//function common header crm links
const eventDetailsHeaderLinks = (id, t, slug = "") => {
  const basePath = `/admin/events`;

  // console.log("slug in helper", slug);

  // Helper function to create link objects
  const createLink = (title, path) => ({
    title: typeof title === "string" ? title : t(title),
    link: `${basePath}/${path}/${id}`,
  });

  // Common links that appear in both cases
  const commonLinks = [
    createLink(t("Event Info"), "info"),
    createLink(t("Sub Events"), "subevents"),
    createLink(t("Members"), "members"),
  ];

  // Links that always appear at the end
  const endingLinks = [
    createLink(t("Gallery"), "gallery"),
    createLink("Feedback 360", "feedbackrules"),
  ];

  if (slug === "buysell") {
    return [
      ...commonLinks,
      createLink(t("Products"), "products"),
      createLink(t("Buy Quotations"), "quotations/buy"),
      createLink(t("Sell Quotations"), "quotations/sell"),
      ...endingLinks,
    ];
  }

  return [
    ...commonLinks,
    createLink(t("Partners"), "eventpartner"),
    createLink(t("Agenda"), "agenda"),
    ...endingLinks,
  ];

  // let headerLinks = [];

  // if (slug === "buysell") {
  //   headerLinks = [
  //     { title: t("Event Info"), link: `${basePath}/info/${id}` },
  //     { title: t("Sub Events"), link: `${basePath}/subevents/${id}` },
  //     { title: t("Members"), link: `${basePath}/members/${id}` },
  //     { title: t("Products"), link: `${basePath}/products/${id}` },
  //     { title: t("Buy Quotations"), link: `${basePath}/quotations/buy/${id}` },
  //     {
  //       title: t("Sell Quotations"),
  //       link: `${basePath}/quotations/sell/${id}`,
  //     },
  //     { title: t("Gallery"), link: `${basePath}/gallery/${id}` },
  //     { title: "Feedback", link: `${basePath}/feedback/${id}` },
  //   ];
  // } else {
  //   headerLinks = [
  //     { title: t("Event Info"), link: `${basePath}/info/${id}` },
  //     { title: t("Sub Events"), link: `${basePath}/subevents/${id}` },
  //     { title: t("Members"), link: `${basePath}/members/${id}` },
  //     { title: t("Partners"), link: `${basePath}/eventpartner/${id}` },
  //     { title: t("Agenda"), link: `${basePath}/agenda/${id}` },
  //     { title: t("Gallery"), link: `${basePath}/gallery/${id}` },
  //     { title: "Feedback", link: `${basePath}/feedback/${id}` },
  //   ];
  // }

  // return headerLinks;
};

//get duration
const getDuration = (startTimeStr, endTimeStr) => {
  // Convert start time and end time to Date objects
  const startTime = new Date(`2000-01-01T${startTimeStr}:00`);
  const endTime = new Date(`2000-01-01T${endTimeStr}:00`);

  // Calculate the duration in milliseconds
  const durationMs = endTime - startTime;

  // Convert the duration to hours and minutes
  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

  // Format the duration as "hh:mm"
  const durationStr = `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;

  return durationStr;
};

export { eventDetailsHeaderLinks, getDuration };
