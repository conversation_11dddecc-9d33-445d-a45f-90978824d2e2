/* eslint-disable */
import React, { useEffect, useState, useMemo } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Material UI table
import { MaterialReactTable } from "material-react-table";
//Material UI components for rendring menu and others
import { MenuItem, Box } from "@mui/material";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData } from "utils/Gateway";

import AddLabelModal from "components/Common/Modal/AddLabelModal";
import AlertNotification from "components/Common/AlertNotification/AlertNotification";

import { crmCommonHeaderLinkLists } from "helper/CrmHelper/CrmHelper";
import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import CrmNoteHeader from "./Header/CrmNoteHeader";
import SaveNotePopup from "./Popup/SaveNotePopup";
import AddNewNotePopup from "./Popup/AddNewNotePopup";
import NoteListFilterPopup from "./Popup/NoteListFilterPopup";

const CrmNoteListBody = () => {
  const moduleSlug = "opportunity";

  /* ------------- Language translation imports starts here ------------- */
  const { t, i18n } = useTranslation();
  const todayValue = new Date().toISOString().split("T")[0];
  const moduleAccess = localStorage.getItem("moduleaccess");
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const commonHeaderObject = crmCommonHeaderLinkLists(t);

  const [isLoading, setIsLoading] = useState(false);
  const [noteList, setNoteList] = useState([]);

  //optionally, you can manage the row selection state yourself
  const [rowSelection, setRowSelection] = useState({});
  const [selectedNoteIds, setSelectedNoteIds] = useState([]);

  const [selectedNoteId, setSelectedNoteId] = useState(null);

  const [filterStartDate, setFilterStartDate] = useState("");
  const [filterEndDate, setFilterEndDate] = useState("");
  const [filterTitle, setFilterTitle] = useState("");
  const [filterDetails, setfilterDetails] = useState("");
  const [filterLead, setFilterLead] = useState(null);
  const [filterContact, setFilterContact] = useState(null);
  const [filterOpportunity, setFilterOpportunity] = useState(null);
  const [filterTask, setfilterTask] = useState(null);
  const [filterOwner, setFilterOwner] = useState(null);
  const [filterTags, setFilterTags] = useState([]);

  const [reloadData, setReloadData] = useState(false);
  const [isFilterReset, setIsFilterReset] = useState(false);

  // json filter query
  const [jsonFilterQuery, setJsonFilterQuery] = useState([]);

  const [isNoDefaultFilter, setIsNoDefaultFilter] = useState(false);

  // Pagination -------------------------------------------
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10, //customize the default page size
  });

  const [lastPagination, setLastPagination] = useState({
    pageIndex: 0,
    pageSize: 10, //customize the default page size
  });
  // paginatin ends -----------------------------------

  //alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  //function for get all contacts
  const getNoteList = async () => {
    setRowSelection({});
    setSelectedNoteIds([]);

    try {
      setIsLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_CRM_NOTE +
        `?token=${token}&filternotetype=1`;

      // if (filterStartDate !== "" && filterEndDate !== "") {
      //   requestUrl += `&filterstartdate=${filterStartDate}&filterenddate=${filterEndDate}`;
      // }

      // if (filterTitle != "") {
      //   requestUrl = requestUrl + `&title=${filterTitle}`;
      // }

      // if (filterDetails != "") {
      //   requestUrl = requestUrl + `&details=${filterDetails}`;
      // }

      // if (filterLead) {
      //   requestUrl += `&leadid=${filterLead}`;
      // }

      // if (filterContact) {
      //   requestUrl += `&contactid=${filterContact}`;
      // }

      // if (filterOpportunity) {
      //   requestUrl += `&invoiceid=${filterOpportunity}`;
      // }

      // if (filterTask) {
      //   requestUrl += `&taskid=${filterTask}`;
      // }

      // if (filterOwner) {
      //   requestUrl += `&filterowner=${filterOwner}`;
      // }

      // if (filterTags.length > 0) {
      //   requestUrl += `&filtertags=${filterTags}`;
      // }

      if (jsonFilterQuery.length > 0) {
        // Sanitize the jsonFilterQuery
        const sanitizedQuery = jsonFilterQuery.map((filter) => {
          if (
            (filter.operator === "includes" ||
              filter.operator === "excludes") &&
            Array.isArray(filter.value)
          ) {
            return {
              ...filter,
              value: filter.value.map((item) => item.value).join(","), // Extract and join values as a comma-separated string
            };
          }
          return filter;
        });

        requestUrl = requestUrl + `&filters=${JSON.stringify(sanitizedQuery)}`;
      }

      console.log("request url------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response ------>", response);

      setIsLoading(false);

      if (response.status) {
        setNoteList(response.data);
        // resetFilterData();
        setMessageType("success");
      } else {
        setMessageType("error");
      }

      setAlertMessage(response.message);
    } catch (error) {
      setAlertMessage(error.message);
      setMessageType("error");
    }

    setShowAlert(true);
  };

  //function for change status
  const changeStatusHandler = async () => {
    let responseArr = [];
    for (let selectedId of selectedNoteIds) {
      try {
        let invoiceData = {
          status: "0",
        };
        let requestUrl =
          url.API_BASE_URL +
          url.API_DELETE_INVOICE +
          `/${selectedId}` +
          `?token=${token}`;

        const response = await putData(requestUrl, invoiceData);

        if (response.status) {
          responseArr.push(true);
          setMessageType("success");
        } else {
          setMessageType("error");
        }

        setAlertMessage(response.message);
      } catch (error) {
        setAlertMessage(error.message);
        setMessageType("error");
      }

      setShowAlert(true);
    }

    if (responseArr.length > 0) {
      setTimeout(() => {
        getNoteList();
      }, 1500);
    }
  };

  //material table columns array
  const initialTableColumns = [
    {
      accessorKey: "#",
      header: t("Action"),
      enableColumnActions: false, // Hides the column action icon
      enableColumnDragging: false, // Hides the move icon
      enableSorting: false,
      Cell: ({ row }) => (
        <button
          className="action_btn_mui"
          data-bs-toggle="offcanvas"
          data-bs-target="#saveNote"
          aria-controls="saveNote"
          onClick={() => {
            setSelectedNoteId(row.original._id);
          }}
        >
          <span className="d-block material-symbols-outlined horz_icon">
            more_horiz
          </span>
        </button>
      ),
    },
    {
      accessorKey: "notetitle",
      header: t("Title"),
      size: 200,
    },
    {
      accessorKey: "tagstring",
      header: t("Label"),
      size: 200,
      Cell: ({ row }) => (
        <div className="label border-bottom-0">
          {row.original.tagstring != "" && (
            <ul className="d-flex flex-wrap gap-2 fs-xs">
              {row.original.tagstring.split(" , ").map((tag, index) => {
                return (
                  <li
                    key={index}
                    className="px-2 py-1 gradient-light rounded-5"
                  >
                    {tag}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      ),
    },
    {
      accessorKey: "moduleslug",
      header: t("Module"), //lead title
      size: 150,
    },
    {
      accessorKey: "componentname",
      header: t("Component"), //lead,contact
      size: 200,
      Cell: ({ row }) => (
        <div className="title border-bottom-0">
          <div className="d-flex align-items-center gap-4">
            <Link
              to={
                row.original.moduleslug === "lead"
                  ? `/admin/crm/lead/information/${row.original.leadid}`
                  : row.original.moduleslug === "contact"
                  ? `/admin/crm/contact/details/timeline/${row.original.contactid}`
                  : row.original.moduleslug === "opportunity"
                  ? `/admin/crm/opportunity/save/${row.original.invoiceid}`
                  : row.original.moduleslug === "invoice"
                  ? `/admin/invoice/details/${row.original.invoiceid}`
                  : row.original.moduleslug === "task"
                  ? `/admin/task/details/${row.original.taskid}`
                  : row.original.moduleslug === "invoicedoc"
                  ? `/admin/invoicedoc/details/${row.original.invoicedocid}`
                  : "#"
              }
              className="d-flex align-items-center gap-1 fs-md"
            >
              {row.original.componentname}
            </Link>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "notedate",
      header: t("Date"),
      size: 200,
    },
    {
      accessorKey: "ownerofcomponent",
      header: t("Owner of Element"),
      size: 200,
    },
    {
      accessorKey: "moderatorname",
      header: t("Owner of Note"),
      size: 200,
    },
  ];

  //* Material React Table Column and States */
  const columns = useMemo(() => initialTableColumns, [i18n.language]);

  /* Updates the column visibility state by toggling the visibility of the column with the given accessor key.*/
  const initialVisibilityState = initialTableColumns.reduce((acc, column) => {
    acc[column.accessorKey] = true;
    return acc;
  }, {});

  /* Represents the state of column visibility in the table.*/
  const [visibleColoumns, setVisibleColoumns] = useState(
    initialVisibilityState
  );

  //function for column visibility
  const onColumnVisiblityHandler = (newColumnState) => {
    if (typeof newColumnState === "function") {
      const newColumnStateName = newColumnState();
      setVisibleColoumns((prev) => ({ ...prev, ...newColumnStateName }));
    } else {
      setVisibleColoumns(newColumnState);
    }
  };

  //initialize the column order
  const initialColumnOrder = [
    "#",
    "mrt-row-select",
    ...columns.map((c) => c.accessorKey),
  ]; //array of column ids (Initializing is optional as of v2.10.0)

  const [columnOrder, setColumnOrder] = useState(initialColumnOrder);

  //function for change column order
  const changeColumnOrderHandler = (changedOrder) => {
    setColumnOrder(changedOrder);
  };

  const resetFilterData = () => {
    setJsonFilterQuery([]);
    setFilterStartDate("");
    setFilterEndDate("");
    setFilterTitle("");
    setfilterDetails("");
    setFilterLead(null);
    setFilterContact(null);
    setFilterOpportunity(null);
    setfilterTask(null);
    setFilterOwner(null);
    setFilterTags([]);

    setLastPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });

    setReloadData(true);
  };

  useEffect(() => {
    if (jsonFilterQuery.length > 0) {
      getNoteList();
    }
  }, [jsonFilterQuery]);

  useEffect(() => {
    getNoteList();
  }, []);

  // useEffect(() => {
  //   if (isNoDefaultFilter) {
  //     getNoteList();
  //   }
  // }, [isNoDefaultFilter]);

  useEffect(() => {
    const selectedIdsArray = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );

    if (selectedIdsArray.length > 0) {
      setSelectedNoteIds(selectedIdsArray);
    } else {
      setSelectedNoteIds([]);
    }
  }, [rowSelection]);

  useEffect(() => {
    // if (pagination.pageIndex > 0 || pagination.pageSize > 10) {
    //   setLastPagination({
    //     pageIndex: pagination.pageIndex,
    //     pageSize: pagination.pageSize,
    //   });
    // }

    setLastPagination({
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
    });
  }, [pagination.pageIndex, pagination.pageSize]);

  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  const refreshRecords = () => {
    resetFilterData();
    setVisibleColoumns(initialVisibilityState);
    setColumnOrder(initialColumnOrder);
  };

  useEffect(() => {
    if (reloadData) {
      setIsFilterReset(true);
      getNoteList();
      setReloadData(false);
    }
  }, [reloadData]);

  return (
    <div id="content_wrapper">
      <section className="crm-wrapper bg-white pb-5">
        <TabsHeader
          commonHeaderObject={commonHeaderObject}
          activeOption={t("Notes")}
        />
        <div className="container-fluid px-lg-5">
          <CrmNoteHeader reloadList={refreshRecords} />

          {isLoading ? (
            <div className="placeholder-glow d-flex flex-column gap-4">
              <span className="placeholder placeholder-lg bg-secondary col-12"></span>
              <span className="placeholder placeholder-lg bg-secondary col-8"></span>
              <span className="placeholder placeholder-lg bg-secondary col-4"></span>
            </div>
          ) : (
            <div className="table-wrapper">
              <MaterialReactTable
                columns={columns} // map columns to be displayed with api data,
                data={noteList} // data from api to be displayed
                positionActionsColumn="last"
                enableGrouping // to enable grouping of column
                enableRowSelection // enable showing checkbox
                getRowId={(row) => row._id} // map which value to select with row checkbox
                onRowSelectionChange={setRowSelection} //connect internal row selection state to your own
                state={{
                  rowSelection,
                  pagination: lastPagination,
                  columnVisibility: visibleColoumns,
                  columnOrder: columnOrder,
                }} //pass our managed row selection state to the table to use
                onPaginationChange={setPagination} // set pagination
                initialState={{
                  rowSelection,
                  pagination: lastPagination,
                  columnVisibility: visibleColoumns,
                  columnOrder: columnOrder,
                }}
                enableColumnOrdering={true}
                onColumnVisibilityChange={onColumnVisiblityHandler}
                onColumnOrderChange={changeColumnOrderHandler}
                defaultColumn={{
                  minSize: 20,
                  maxSize: 200,
                  size: 50, //make columns wider by default
                }}
                muiTableContainerProps={{
                  sx: {
                    maxHeight: "60vh",
                  },
                }}
                enableStickyHeader
              />
            </div>
          )}
        </div>
      </section>

      {/* create new note in any component */}
      <AddNewNotePopup
        setShowAlert={setShowAlert}
        setAlertMessage={setAlertMessage}
        setMessageType={setMessageType}
        afterPopupClose={() => {
          setTimeout(() => {
            getNoteList();
          }, 2500);
        }}
      />

      {/* update or add new note in same component */}
      <SaveNotePopup
        selectedNoteId={selectedNoteId}
        setSelectedNoteId={setSelectedNoteId}
        setShowAlert={setShowAlert}
        setAlertMessage={setAlertMessage}
        setMessageType={setMessageType}
        afterPopupClose={() => {
          setTimeout(() => {
            getNoteList();
          }, 2500);
        }}
      />

      <NoteListFilterPopup
        moduleSlug="crmnotes"
        setJsonFilterQuery={setJsonFilterQuery}
        reloadList={refreshRecords}
        isFilterReset={isFilterReset}
        setIsFilterReset={setIsFilterReset}
      />

      <AddLabelModal
        moduleName="crmnote"
        selectedIds={selectedNoteIds}
        afterTagModalClose={() => {
          setTimeout(() => {
            getNoteList();
          }, 2500);
        }}
        setShowAlert={setShowAlert}
        setAlertMessage={setAlertMessage}
        setMessageType={setMessageType}
      />

      {showAlert && (
        <AlertNotification
          showAlert={showAlert}
          message={alertMessage}
          alertType={messageType}
          onClose={onAlertClose}
        />
      )}
    </div>
  );
};

export default CrmNoteListBody;
