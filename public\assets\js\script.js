jQuery(document).ready(function(){
    jQuery('header#header #offcanvasMainNav ul.nav li a').click(function(e){
        if(jQuery(this).parent().find('ul').length > 0){
            e.preventDefault();
            if(jQuery(this).parent().hasClass('opened')){
                jQuery(this).parent().removeClass('opened').find('ul').slideUp(300);
            }else{
                jQuery(this).parent().addClass('opened').find('ul').slideDown(300);
                jQuery(this).parent().siblings().removeClass('opened').find('ul').slideUp(300);
            }
        }
    });
    $('.notification .dropdown-menu').on('click', function (e) {
        e.stopPropagation();
    });
    // $( ".datepicker" ).datepicker();

    $(".open_rvw_trigger a").click(function(){
        $("body").toggleClass("open-review")
    })
    $(".overlay").click(function(){
        $("body").removeClass("open-review")
    })
});