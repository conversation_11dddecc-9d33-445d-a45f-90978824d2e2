import React, { useEffect } from "react";
import Header from "components/Common/Header/Header";
import AutomationEventsListBody from "components/AutomationComponents/AutomationEvents/AutomationEventsListBody/AutomationEventsListBody";

const AutomationEventsList = () => {
  // -------- for title ------------
  useEffect(() => {
    document.title = "Automation Events";
  }, []);

  return (
    <main id="app">
      {/* ---- common header ---- */}
      <Header moduleName="automation" />

      <AutomationEventsListBody />
    </main>
  );
};

export default AutomationEventsList;
