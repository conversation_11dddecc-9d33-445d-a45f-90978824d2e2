/* eslint-disable */
import React, { useEffect, useRef, useState, useMemo } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Material UI table
import { MaterialReactTable } from "material-react-table";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData, uploadMultipleFile } from "utils/Gateway";

import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import { eventDetailsHeaderLinks } from "helper/EventHelper/EventHelper";
import AccessDeniedView from "components/Common/AccessDeniedView/AccessDeniedView";
import BreadCrumb from "components/Common/BreadCrumb/BreadCrumb";
import EventFeedbackRuleHeader from "../Header/EventFeedbackRuleHeader";
import AlertNotification from "components/Common/AlertNotification/AlertNotification";
import SaveFeedbackRulePopup from "components/Common/Popup/SaveFeedbackRulePopup";
import FeedbackRuleFilter from "components/Common/Popup/FeedbackRuleFilter";
import AddLabelModal from "components/Common/Modal/AddLabelModal";

const EventFeedbackRulesBody = () => {
  const { t, i18n } = useTranslation(); //for translation
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");

  const breadCrumbText = [
    { title: t("Event"), link: "/admin/events/list" },
    { title: t("Gallery") },
  ];

  const [commonHeaderObject, setCommonHeaderObject] = useState([]); // State for header links
  const [parentEventTitle, setParentEventTitle] = useState("");

  const [isEventModerator, setisEventModerator] = useState(false);

  const [eventMemberStatus, setEventMemberStatus] = useState("");
  const [eventModStatus, setEventModStatus] = useState("");
  const [isEventDetailsLoading, setIsEventDetailsLoading] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [rulesList, setrulesList] = useState([]); // State for rules list

  const [rowSelection, setRowSelection] = useState({});
  const [selectedfeedbackRuleIds, setSelectedFeedbackRuleIds] = useState([]);
  const [selectedfeedbackRuleId, setSelectedFeedbackRuleId] = useState(null);

  const [isReload, setIsReload] = useState(false);
  const [isFilterReset, setIsFilterReset] = useState(false);

  // json filter query
  const [jsonFilterQuery, setJsonFilterQuery] = useState([]);

  //* alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  //function for get details of event member
  const getMemberDetails = async () => {
    setIsEventDetailsLoading(true);
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_EVENT_MEMBER_DETAILS_BY_QUERY +
        `?token=${token}&eventid=${params.id}`;

      const response = await getData(requestUrl);

      console.log("response in member details", response);

      if (response.status) {
        setEventMemberStatus(response.data.memberstatus);
        setEventModStatus(response.data.moderatorstatus);
      } else {
        setAlertMessage(response.message);
        setMessageType("error");
        setShowAlert(true);
      }

      setIsEventDetailsLoading(false);
    } catch (error) {
      setAlertMessage(error.message);
      setMessageType("error");
      setShowAlert(true);
    }
  };

  //function for get event details
  const getEventDetails = async () => {
    try {
      setSelectedFeedbackRuleIds([]);
      setRowSelection({});

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_EVENT_DETAILS +
        `/${params.id}?token=${token}`;

      console.log("url of event gallery------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response of event gallery------>", response);

      if (response.status && response.data) {
        setParentEventTitle(response.data?.title);

        // Assign slug and update header links
        // response.data?.slug && setEventSlug(response.data?.slug);
        // set event type slug
        setCommonHeaderObject(
          eventDetailsHeaderLinks(params.id, t, response.data?.eventtype?.slug)
        );

        let isModerator = false;

        if (
          response.data.moderator._id.toString() === userInfo._id.toString() ||
          userInfo.role.slug === "ADMIN" ||
          userInfo.role.slug === "SUPER_ADMIN"
        ) {
          isModerator = true;
        } else {
          getMemberDetails();
        }

        setisEventModerator(isModerator);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //get all feedback rules
  const getAllFeedbackRules = async () => {
    try {
      setIsLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_FEEDBACK_RULES +
        `?token=${token}&eventid=${params.id}`;

      if (jsonFilterQuery.length > 0) {
        // Sanitize the jsonFilterQuery
        const sanitizedQuery = jsonFilterQuery.map((filter) => {
          if (
            (filter.operator === "includes" ||
              filter.operator === "excludes") &&
            Array.isArray(filter.value)
          ) {
            return {
              ...filter,
              value: filter.value.map((item) => item.value).join(","), // Extract and join values as a comma-separated string
            };
          }
          return filter;
        });

        requestUrl = requestUrl + `&filters=${JSON.stringify(sanitizedQuery)}`;
      }

      const response = await getData(requestUrl);

      console.log("response of feedback rules------>", response);

      setIsLoading(false);

      if (response.status) {
        setrulesList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //delete bulk
  const deleteBulkFeedbackRules = async () => {
    if (selectedfeedbackRuleIds.length > 0) {
      try {
        const listToDelete = rulesList
          .filter((item) => selectedfeedbackRuleIds.includes(item._id))
          .map((item) => item._id);

        let apiData = {
          feedbackrules: listToDelete,
        };

        let requestURL =
          url.API_BASE_URL +
          url.API_DELETE_BULK_FEEDBACK_RULES +
          `?token=${token}`;

        const response = await putData(requestURL, apiData);

        console.log(response);

        if (response.status) {
          setMessageType("success");

          setTimeout(() => {
            resetFilterData();
          }, 2500);
        } else {
          setMessageType("error");
        }

        setAlertMessage(response.message);
      } catch (error) {
        setAlertMessage(error.message);
        setMessageType("error");
      }
    } else {
      setMessageType("error");
      setAlertMessage("Please select atleast one feedback rule");
    }
    setShowAlert(true);
  };

  //function for reset filter
  const resetFilterData = () => {
    setIsFilterReset(true);
    setJsonFilterQuery([]);
    setIsReload(true);
  };

  useEffect(() => {
    if (jsonFilterQuery && jsonFilterQuery.length > 0) {
      getAllFeedbackRules();
    }
  }, [jsonFilterQuery]);

  useEffect(() => {
    if (isReload) {
      getAllFeedbackRules();
      setIsReload(false);
    }
  }, [isReload]);

  useEffect(() => {
    if (params.id) {
      getEventDetails();
      getAllFeedbackRules();
    }
  }, [params.id]);

  /*
   * Material React Table Column and States -----------------------------------------------------------
   *---------------------------------------------------------------------------------------------------
   */
  const columns = useMemo(
    () => [
      {
        accessorKey: "#",
        header: t("Actions"),
        Cell: ({ row }) => (
          <button
            className="action_btn_mui"
            data-bs-toggle="offcanvas"
            data-bs-target="#savefeedbackrule"
            aria-controls="savefeedbackrule"
            onClick={() => {
              setSelectedFeedbackRuleId(row.original._id);
            }}
          >
            <span className="d-block material-symbols-outlined horz_icon">
              more_horiz
            </span>
          </button>
        ),
        enableColumnActions: false, // Hides the column action icon
        enableColumnDragging: false, // Hides the move icon
        enableSorting: false,
      },
      {
        accessorKey: "customid",
        header: t("Id"),
        size: 100,
      },
      {
        accessorKey: "tagstring",
        header: t("Label"),
        Cell: ({ row }) => (
          <div className="label border-bottom-0">
            {row.original.tagstring && (
              <ul className="d-flex flex-wrap gap-2 fs-xs">
                {row.original.tagstring.split(" , ").map((tag, index) => {
                  return (
                    <li
                      key={index}
                      className="px-2 py-1 gradient-light rounded-5"
                    >
                      {tag}
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        ),
      },
      {
        accessorKey: "title",
        header: t("Name"),
        size: 200,
      },
      {
        accessorKey: "slug",
        header: t("Slug"),
        size: 200,
      },
      {
        accessorKey: "senderrolename",
        header: t("Sender Role"),
        size: 200,
      },
      {
        accessorKey: "recieverrolename",
        header: t("Receiver Role"),
        size: 200,
      },
      {
        accessorKey: "surveyname",
        header: t("Survey"),
        size: 200,
      },
      {
        accessorKey: "createddate",
        header: t("Date"),
        size: 200,
      },
    ],
    [i18n.language]
  );

  //initialize the column order
  const columnOrder = [
    "#",
    "mrt-row-select",
    ...columns.map((c) => c.accessorKey),
  ]; //array of column ids (Initializing is optional as of v2.10.0)

  useEffect(() => {
    //do something when the row selection changes...
    // console.info("rowSelection", rowSelection);
    const selectedIdsArray = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );
    if (selectedIdsArray.length > 0) {
      setSelectedFeedbackRuleIds(selectedIdsArray);
    } else {
      setSelectedFeedbackRuleIds([]);
    }
  }, [rowSelection]);

  //* This is a function that is called when the alert close button is clicked.
  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_EVENT")
  ) {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper crm-conversation-wrapper bg-white pb-5">
          {/* ---- common header ---- */}
          {params.id && (
            <TabsHeader
              commonHeaderObject={commonHeaderObject}
              activeOption={t("Feedback 360")}
            />
          )}
          <div className="container-fluid px-lg-5 pt-4 pt-md-0">
            <BreadCrumb
              breadCrumbText={breadCrumbText}
              bottom={true}
              displayName={`${parentEventTitle}`}
            />

            <EventFeedbackRuleHeader
              reloadList={resetFilterData}
              deleteBulk={deleteBulkFeedbackRules}
            />

            {isLoading ? (
              <div className="placeholder-glow d-flex flex-column gap-4">
                <span className="placeholder placeholder-lg bg-secondary col-12"></span>
                <span className="placeholder placeholder-lg bg-secondary col-8"></span>
                <span className="placeholder placeholder-lg bg-secondary col-4"></span>
              </div>
            ) : (
              <div className="table-wrapper">
                <MaterialReactTable
                  columns={columns} // map columns to be displayed with api data,
                  data={rulesList} // data from api to be displayed
                  positionActionsColumn="last"
                  enableGrouping // to enable grouping of column
                  enableRowSelection // enable showing checkbox
                  getRowId={(row) => row._id} // map which value to select with row checkbox
                  onRowSelectionChange={setRowSelection} //connect internal row selection state to your own
                  state={{ rowSelection, columnOrder }} //pass our managed row selection state to the table to use
                  defaultColumn={{
                    minSize: 20,
                    maxSize: 200,
                    size: 50, //make columns wider by default
                  }}
                  muiTableContainerProps={{
                    sx: {
                      maxHeight: "60vh",
                    },
                  }}
                  enableStickyHeader
                />
              </div>
            )}

            <FeedbackRuleFilter
              setJsonFilterQuery={setJsonFilterQuery}
              isFilterReset={isFilterReset}
              setIsFilterReset={setIsFilterReset}
              reloadList={resetFilterData}
            />

            <SaveFeedbackRulePopup
              feedbackRuleId={selectedfeedbackRuleId}
              setFeedbackRuleId={setSelectedFeedbackRuleId}
              afterSaveData={resetFilterData}
              setShowAlert={setShowAlert}
              setAlertMessage={setAlertMessage}
              setMessageType={setMessageType}
            />

            <AddLabelModal
              selectedIds={selectedfeedbackRuleIds}
              moduleName="feedbackrule"
              afterTagModalClose={resetFilterData}
              setShowAlert={setShowAlert}
              setAlertMessage={setAlertMessage}
              setMessageType={setMessageType}
            />

            {showAlert && (
              <AlertNotification
                showAlert={showAlert}
                message={alertMessage}
                alertType={messageType}
                onClose={onAlertClose}
              />
            )}
          </div>
        </section>
      </div>
    );
  } else {
    return <AccessDeniedView />;
  }
};

export default EventFeedbackRulesBody;
