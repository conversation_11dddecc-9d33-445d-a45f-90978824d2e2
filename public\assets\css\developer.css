.empty_access {
  padding: 160px 100px;
}

.tag .active {
  background: rgba(var(--bs-primary-rgb), 0.26) !important;
  color: var(--bs-primary);
  border-color: rgba(var(--bs-primary-rgb), 0.26) !important;
}

.upload_img {
  max-width: 100px;
}

.action_link_block_bg {
  background-color: #f4f4f4;
}

.action_link_block_bg label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.625rem;
  letter-spacing: -0.2px;
}

img.thumb-25 {
  max-width: 25px;
  max-height: 25px;
}

/* kanban board css */
.kanban_column {
  max-height: 40rem;
  overflow-y: scroll;
}

/* disable it */
.dropdown-toggle::after {
  display: none !important;
}

/***** Mui Css Section *****/
.MuiBox-root {
  width: max-content;
}
/***** End Mui Css Section *****/

/***** editor css Section *****/
.tox .tox-editor-header {
  z-index: 0 !important;
}
/***** End editor css Section *****/

.upload_content_sec .process_upload {
  height: 135px;
}

.checkbox_select_input_box {
  max-width: 8rem;
}

/* css for responsive */
@media (max-width: 394px) {
  header#header .navbar-nav {
    position: relative;
  }

  header#header .navbar-nav .dropdown-menu {
    position: absolute;
  }
}

/* kanban view css */
.react-kanban-board {
  background-color: #fafafa;
}

.react-kanban-column {
  background-color: transparent !important;
}

/* ul.list_style_none {
  display: flex;
  justify-content: space-between;
}

ul.list_style_none li:last-child {
  margin-left: auto;
} */

img.small-thumb {
  width: 22px;
  height: 22px;
}
.boardpnl_date {
  display: flex;
  align-items: center;
  color: var(--bs-body-color);
  margin-bottom: 15px;
}

.react-kanban-column-header {
  padding: 15px;
  background-color: rgba(var(--bs-primary-rgb), 0.26);
  color: var(--bs-white-color);
  border-radius: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
}

.react-kanban-card {
  background-color: #fff;
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
}

.border-grey {
  border: 1px solid #ccc;
}

.large-checkbox {
  width: 24px;
  height: 24px;
}

/* material ui table css start here */
.action_btn_mui {
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: #ffffff;
}

.action_btn_mui:hover {
  background-color: #e2e2e2;
}

.action_btn_mui > .horz_icon {
  color: #c3c3c3;
}

.action_btn_mui > .horz_icon:hover {
  color: #7d7d7d;
}
/* material ui table css end here */

.task_logs {
  max-height: 20rem;
}

.invoice_w {
  width: 95% !important;
}

.dev_card_collapsed {
  min-width: 100px;
}

.black-space-card {
  height: 460px;
  width: 300px;
}

/* challenges */
.gray_bx_item {
  width: 25%;
  padding: 12px;
  background-color: #f2f6fd;
  border-radius: 8px;
}

.jodit-ui-input_focused_false .jodit-ui-input__input {
  pointer-events: auto !important;
  opacity: 1 !important;
}

/* category css  */
.categories-container {
  max-height: 400px;
  overflow-y: auto;
}

/* challange type modal style */
/* Hover effect for cards */
.challenge-type-card {
  transition: all 0.3s ease-in-out;
}

.challenge-type-card:hover {
  background-color: #007bff; /* Change to your desired hover background color */
  color: #fff; /* Change text color on hover */
  transform: translateY(-5px); /* Slight lift effect */
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2); /* Add shadow on hover */
}

.icon-container span {
  transition: color 0.3s ease-in-out;
}

.challenge-type-card:hover .icon-container span {
  color: #fff; /* Change icon color on hover */
}

/* Center cards if only two are present */
.row.justify-content-center {
  justify-content: center !important;
}

.custom-offcanvas {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add box shadow to the offcanvas */
  border-left: 1px solid #ddd; /* Optional: Add a border for better separation */
}

.custom-offcanvas .offcanvas-body {
  overflow-y: auto; /* Ensure content scrolls if it overflows */
}
