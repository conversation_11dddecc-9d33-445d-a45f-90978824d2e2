import React, { useState, useEffect, useRef, useMemo } from "react";

/*import url and gateway methods */
import { postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const AddMultipleLeadsModal = ({ show, handleClose, reloadData }) => {
  // const moduleAccess = localStorage.getItem("moduleaccess");
  const token = localStorage.getItem("token");
  // const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  // Default lead state
  const defaultLeadState = [
    { name: "", surname: "", email: "", phone: "", company: "", notes: "" },
  ];

  const [leads, setLeads] = useState(defaultLeadState);

  // New state to track if we should show duplicate indicators
  const [showDuplicateIndicators, setShowDuplicateIndicators] = useState(false);

  // Create a ref array to store references to name input fields
  const nameInputRefs = useRef([]);

  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const handleInputChange = (index, field, value) => {
    const updatedLeads = [...leads];
    updatedLeads[index][field] = value;
    setLeads(updatedLeads);

    // Clear error when user edits
    setErrorMessage("");
    setShowDuplicateIndicators(false);
  };

  const handleDeleteRow = (index) => {
    const updatedLeads = leads.filter((_, i) => i !== index);
    setLeads(updatedLeads);
    setErrorMessage(""); // Clear error on delete
    setShowDuplicateIndicators(false);
  };

  const handleAddRow = () => {
    // Validate last row only if it's not the first row
    if (leads.length > 0) {
      setShowDuplicateIndicators(true);

      const lastRow = leads[leads.length - 1];
      const lastEmail = lastRow.email.trim();

      // Check if email exists in any previous row
      if (lastEmail) {
        const duplicateFound = leads
          .slice(0, -1)
          .some((lead) => lead.email.trim() === lastEmail);

        if (duplicateFound) {
          setErrorMessage(
            `The email "${lastEmail}" is already entered. Please enter a different email.`
          );
          return;
        }
      }
    }

    // Clear error if no duplicates
    setErrorMessage("");

    // Proceed to add new row
    const newLeads = [
      ...leads,
      { name: "", surname: "", email: "", phone: "", company: "", notes: "" },
    ];

    setLeads(newLeads);

    // Focus on new row
    setTimeout(() => {
      const newIndex = newLeads.length - 1;
      if (nameInputRefs.current[newIndex]) {
        nameInputRefs.current[newIndex].focus();
      }
    }, 0);
  };

  // Create a custom close handler that resets the leads state
  const handleCloseAndReset = () => {
    setLeads(defaultLeadState);
    setShowDuplicateIndicators(false);
    setErrorMessage("");
    handleClose();
  };

  // Compute duplicate email indices
  const duplicateEmailIndices = useMemo(() => {
    if (!showDuplicateIndicators) return new Set();

    const duplicates = new Set();
    const seenEmails = new Set();

    leads.forEach((lead, index) => {
      const email = lead.email.trim().toLowerCase();
      if (email) {
        if (seenEmails.has(email)) {
          duplicates.add(index);
          // Also mark the first occurrence
          leads.forEach((l, i) => {
            if (l.email.trim().toLowerCase() === email && i !== index) {
              duplicates.add(i);
            }
          });
        } else {
          seenEmails.add(email);
        }
      }
    });

    return duplicates;
  }, [leads, showDuplicateIndicators]);

  // Update refs when leads array changes
  useEffect(() => {
    // Pre-allocate ref array based on leads length
    nameInputRefs.current = nameInputRefs.current.slice(0, leads.length);
  }, [leads.length]);

  const handleKeyPress = (index, field, event) => {
    if (field === "notes" && event.key === "Enter") {
      handleAddRow();
    }
  };

  // Function to handle saving leads
  // send leads using POST method to API_SAVE_QUICK_LEAD
  const handleSave = async () => {
    const isValid = leads.every(
      (lead) => lead.name.trim() && lead.email.trim()
    );

    if (!isValid) {
      alert("Please fill in both Name and Email for all rows.");
      return;
    }

    // Check for duplicate emails
    const emails = leads.map((lead) => lead.email.trim());
    const uniqueEmails = new Set(emails);

    if (emails.length !== uniqueEmails.size) {
      alert("Duplicate emails found. Please correct them before saving.");
      return;
    }

    const leadsData = {
      leads: leads,
    };

    try {
      setIsLoading(true);

      const requestUrl =
        url.API_BASE_URL + url.API_SAVE_QUICK_LEAD + `?token=${token}`;

      const response = await postData(requestUrl, leadsData);

      console.log("response", response);
      setIsLoading(false);

      if (response.status) {
        console.log("Leads saved successfully:", response.data);
        setLeads(defaultLeadState);
        handleCloseAndReset();
        reloadData();
      } else {
        setErrorMessage(response.message || "Failed to save leads.");
      }
    } catch (error) {
      console.error("Error saving leads:", error);
    }
  };

  return (
    <div
      className={`modal fade ${show ? "show d-block" : ""}`}
      tabIndex="-1"
      style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
    >
      <div
        className="modal-dialog modal-lg"
        style={{
          maxWidth: "90%",
        }}
      >
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Add Multiple Leads</h5>
            <button
              type="button"
              className="btn-close"
              onClick={handleCloseAndReset}
            ></button>
          </div>
          <div
            className="modal-body"
            style={{
              overflowX: "auto",
            }}
          >
            {errorMessage && (
              <div className="alert alert-danger mt-2" role="alert">
                {errorMessage}
              </div>
            )}
            <table
              className="table table-bordered"
              style={{
                backgroundColor: "#f8f9fa", // Light grey background
                marginBottom: "0",
                tableLayout: "auto", // Allow columns to adjust to content
              }}
            >
              <thead>
                <tr>
                  <th style={{ padding: "5px", minWidth: "150px" }}>Name</th>
                  <th style={{ padding: "5px", minWidth: "150px" }}>Surname</th>
                  <th style={{ padding: "5px", minWidth: "200px" }}>Email</th>
                  <th style={{ padding: "5px", minWidth: "150px" }}>Phone</th>
                  <th style={{ padding: "5px", minWidth: "200px" }}>Company</th>
                  <th style={{ padding: "5px", minWidth: "200px" }}>Notes</th>
                  <th style={{ padding: "5px", minWidth: "100px" }}>Action</th>
                </tr>
              </thead>
              <tbody>
                {leads.map((lead, index) => (
                  <tr key={index}>
                    <td style={{ padding: "5px" }}>
                      <input
                        type="text"
                        className="form-control"
                        style={{
                          width: "100%",
                          minWidth: "150px",
                        }}
                        placeholder="Enter name"
                        value={lead.name}
                        ref={(el) => (nameInputRefs.current[index] = el)}
                        onChange={(e) =>
                          handleInputChange(index, "name", e.target.value)
                        }
                      />
                    </td>
                    <td style={{ padding: "5px" }}>
                      <input
                        type="text"
                        className="form-control"
                        style={{
                          width: "100%",
                          minWidth: "150px",
                        }}
                        placeholder="Enter surname"
                        value={lead.surname}
                        onChange={(e) =>
                          handleInputChange(index, "surname", e.target.value)
                        }
                      />
                    </td>
                    <td style={{ padding: "5px", position: "relative" }}>
                      <div style={{ position: "relative" }}>
                        <input
                          type="email"
                          className="form-control"
                          style={{
                            width: "100%",
                            minWidth: "200px",
                            paddingRight: "20px", // Space for the star
                          }}
                          placeholder="Enter email"
                          value={lead.email}
                          onChange={(e) =>
                            handleInputChange(index, "email", e.target.value)
                          }
                        />
                        {duplicateEmailIndices.has(index) && (
                          <p
                            className="text-danger fw-bold fs-xl position-absolute"
                            style={{
                              right: "8px",
                              top: "40%",
                              transform: "translateY(-50%)",
                            }}
                          >
                            *
                          </p>
                        )}
                      </div>
                    </td>
                    <td style={{ padding: "5px" }}>
                      <input
                        type="text"
                        className="form-control"
                        style={{
                          width: "100%",
                          minWidth: "150px",
                        }}
                        placeholder="Enter phone"
                        value={lead.phone}
                        onChange={(e) =>
                          handleInputChange(index, "phone", e.target.value)
                        }
                      />
                    </td>
                    <td style={{ padding: "5px" }}>
                      <input
                        type="text"
                        className="form-control"
                        style={{
                          width: "100%",
                          minWidth: "200px",
                        }}
                        placeholder="Enter company"
                        value={lead.company}
                        onChange={(e) =>
                          handleInputChange(index, "company", e.target.value)
                        }
                      />
                    </td>
                    <td style={{ padding: "5px" }}>
                      <input
                        type="text"
                        className="form-control"
                        style={{
                          width: "100%",
                          minWidth: "200px",
                        }}
                        placeholder="Enter notes"
                        value={lead.notes}
                        onChange={(e) =>
                          handleInputChange(index, "notes", e.target.value)
                        }
                        onKeyPress={(e) => handleKeyPress(index, "notes", e)}
                      />
                    </td>
                    <td style={{ padding: "5px" }}>
                      <button
                        className="btn btn-danger btn-sm"
                        onClick={() => handleDeleteRow(index)}
                      >
                        <i className="material-symbols-outlined icon-sm">
                          delete
                        </i>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <button
              className="btn btn-secondary btn-sm mt-2"
              onClick={handleAddRow}
            >
              Add New
            </button>
          </div>
          <div className="modal-footer">
            <button
              className="btn btn-gray btn-sm"
              onClick={handleCloseAndReset}
            >
              Close
            </button>
            <button className="btn btn-primary btn-sm" onClick={handleSave}>
              {isLoading ? (
                <span
                  className="spinner-border spinner-border-sm"
                  role="status"
                  aria-hidden="true"
                ></span>
              ) : (
                "Save"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMultipleLeadsModal;
