/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const FundingRequestModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  // Update formData whenever capitalQuestResponseData changes
  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const [previousFunding, setPreviousFunding] = useState({
    date: "",
    company: "",
    amount: ""
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  const handlePreviousFundingInputChange = (e) => {
    const { name, value } = e.target;
    setPreviousFunding(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddPreviousFunding = () => {
    if (previousFunding.company && previousFunding.amount) {
      setFormData(prevData => ({
        ...prevData,
        previousfunding: [...(prevData.previousfunding || []), { ...previousFunding }]
      }));
      setPreviousFunding({
        date: "",
        company: "",
        amount: ""
      });
    }
  };

  const handleDeletePreviousFunding = (index) => {
    setFormData(prevData => ({
      ...prevData,
      previousfunding: prevData.previousfunding.filter((_, i) => i !== index)
    }));
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
    setPreviousFunding({
      date: "",
      company: "",
      amount: ""
    });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData(prevData => ({
      ...prevData,
      fundingamount: formData.fundingamount,
      proposedvalue: formData.proposedvalue,
      fundinguse: formData.fundinguse,
      expectedrunway: formData.expectedrunway,
      milestones: formData.milestones,
      previousfunding: formData.previousfunding
    }));
    let modal = document.querySelector("#funding_request_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="funding_request_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Funding Request</h3>
                <h5>Provide details about your funding request</h5>
              </div>
              <button type="button" className="close" data-bs-dismiss="modal" aria-label="Close" onClick={resetHandler}>
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Funding Amount</label>
                    <input
                      type="text"
                      className="form-control"
                      name="fundingamount"
                      value={formData.fundingamount || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Funding Amount"
                    />
                  </div>
                  <div className="form-group">
                    <label>Proposed Value</label>
                    <input
                      type="text"
                      className="form-control"
                      name="proposedvalue"
                      value={formData.proposedvalue || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Proposed Value"
                    />
                  </div>
                  <div className="form-group">
                    <label>Funding Use</label>
                    <textarea
                      className="form-control"
                      name="fundinguse"
                      value={formData.fundinguse || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Funding Use"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Expected Runway</label>
                    <input
                      type="text"
                      className="form-control"
                      name="expectedrunway"
                      value={formData.expectedrunway || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Expected Runway"
                    />
                  </div>
                  <div className="form-group">
                    <label>Milestones</label>
                    <textarea
                      className="form-control"
                      name="milestones"
                      value={formData.milestones || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Milestones"
                    ></textarea>
                  </div>

                  {/* Previous Funding Section */}
                  <div className="form-group">
                    <label>Previous Funding</label>
                    <div className="previous-funding-form d-flex gap-2 mb-2">
                      <input
                        type="date"
                        className="form-control"
                        name="date"
                        value={previousFunding.date}
                        onChange={handlePreviousFundingInputChange}
                      />
                      <input
                        type="text"
                        className="form-control"
                        name="company"
                        value={previousFunding.company}
                        onChange={handlePreviousFundingInputChange}
                        placeholder="Company Name"
                      />
                      <input
                        type="number"
                        className="form-control"
                        name="amount"
                        value={previousFunding.amount}
                        onChange={handlePreviousFundingInputChange}
                        placeholder="Amount"
                      />
                      <Link to="#" onClick={handleAddPreviousFunding}>
                        <i className="material-symbols-outlined">add</i>
                      </Link>
                    </div>

                    {/* Previous Funding List */}
                    <div className="previous-funding-list">
                      {formData.previousfunding?.map((funding, index) => (
                        <div key={index} className="funding-item d-flex justify-content-between align-items-center p-2 mb-2 bg-light">
                          <span>
                            {new Date(funding.date).toLocaleDateString()} - {funding.company} (${funding.amount})
                          </span>
                          <Link to="#" className="text-danger" onClick={() => handleDeletePreviousFunding(index)}>
                            <i className="material-symbols-outlined">delete</i>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button onClick={resetHandler} data-bs-dismiss="modal" className="btn gray">
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundingRequestModal;
