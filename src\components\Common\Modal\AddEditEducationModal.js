/* eslint-disable */
import { useTranslation } from "react-i18next";
import { useState } from "react";

const AddEditEducationModal = ({
  educationIndex,
  setEducationIndex,
  educationData,
  setEducationData,
  educationListBlock,
  setEducationListBlock,
}) => {
  const { t } = useTranslation(); //for translation
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};
    if (!educationData.degree.trim()) {
      newErrors.degree = t("Degree is required");
    }
    if (!educationData.year.trim()) {
      newErrors.year = t("Year is required");
    }
    if (!educationData.grade.trim()) {
      newErrors.grade = t("Grade is required");
    }
    if (!educationData.institute.trim()) {
      newErrors.institute = t("Institute is required");
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  //function for add edcuation block
  const addEducationHandler = () => {
    if (validateForm()) {
      setEducationListBlock([...educationListBlock, educationData]);
      closeModalHandler();
    }
  };

  //function for edit education block
  const editEducationBlockHandler = () => {
    if (validateForm()) {
      const updatedEducationList = [...educationListBlock];
      updatedEducationList[educationIndex] = educationData;
      setEducationListBlock(updatedEducationList);
      closeModalHandler();
    }
  };

  //close modal handler
  const closeModalHandler = () => {
    setEducationData({
      degree: "",
      year: "",
      grade: "",
      institute: "",
    });
    setEducationIndex(null);
    setErrors({});

    // close the modal of addLabel
    const bootstrapModal = document.querySelector("#addEducationModal");
    const modal = bootstrap.Modal.getInstance(bootstrapModal);
    modal.hide();
  };

  return (
    <div
      className="modal fade"
      id="addEducationModal"
      tabIndex="-1"
      aria-labelledby="addEducationModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content bg-white border-0 rounded-15">
          {/* ------ modal head section start ----- */}
          <div className="modal-header p-4 pb-0 border-0">
            {/* ------ modal title start ----- */}
            <h2 className="fw-bold mb-0" id="addEducationModalLabel">
              {educationIndex != null
                ? t("Update Education")
                : t("Add Education")}
            </h2>
            {/* ------ modal title end ----- */}
            {/* ------ modal close button start ----- */}
            <button
              type="button"
              className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
              aria-label="Close"
              onClick={closeModalHandler}
            ></button>
            {/* ------ modal close button end ----- */}
          </div>
          {/* ------ modal head section end ----- */}
          {/* ------ modal body section start ----- */}
          <div className="modal-body p-4">
            <form onSubmit={(e) => e.preventDefault()}>
              {/* ------ degree section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="selectTags"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Degree")}
                </label>
                <input
                  type="text"
                  className={`form-control fs-sm shadow-none ${
                    errors.degree ? "is-invalid" : ""
                  }`}
                  placeholder={t("Degree")}
                  value={educationData.degree}
                  onChange={(e) => {
                    setEducationData((prevData) => ({
                      ...prevData,
                      degree: e.target.value,
                    }));
                    if (errors.degree) {
                      setErrors((prev) => ({ ...prev, degree: "" }));
                    }
                  }}
                />
                {errors.degree && (
                  <div className="invalid-feedback">{errors.degree}</div>
                )}
              </div>
              {/* ------ degree section end ----- */}
              {/* ------ year and grade section start ----- */}
              <div className="form-group mb-4">
                <div className="row">
                  {/* ------ year section start ----- */}
                  <div className="col-6">
                    <label
                      htmlFor="yeare"
                      className="d-block fs-sm fw-semibold mb-2"
                    >
                      {t("Year")}
                    </label>
                    <input
                      type="number"
                      className={`form-control fs-sm shadow-none ${
                        errors.year ? "is-invalid" : ""
                      }`}
                      placeholder={t("Year")}
                      value={educationData.year}
                      onChange={(e) => {
                        setEducationData((prevData) => ({
                          ...prevData,
                          year: e.target.value,
                        }));
                        if (errors.year) {
                          setErrors((prev) => ({ ...prev, year: "" }));
                        }
                      }}
                    />
                    {errors.year && (
                      <div className="invalid-feedback">{errors.year}</div>
                    )}
                  </div>
                  {/* ------ year section end ----- */}
                  {/* ------ grade section start ----- */}
                  <div className="col-6">
                    <label
                      htmlFor="grade"
                      className="d-block fs-sm fw-semibold mb-2"
                    >
                      {t("Grade")}
                    </label>
                    <input
                      type="text"
                      className={`form-control fs-sm shadow-none ${
                        errors.grade ? "is-invalid" : ""
                      }`}
                      placeholder={t("Grade")}
                      value={educationData.grade}
                      onChange={(e) => {
                        setEducationData((prevData) => ({
                          ...prevData,
                          grade: e.target.value,
                        }));
                        if (errors.grade) {
                          setErrors((prev) => ({ ...prev, grade: "" }));
                        }
                      }}
                    />
                    {errors.grade && (
                      <div className="invalid-feedback">{errors.grade}</div>
                    )}
                  </div>
                  {/* ------ grade section end ----- */}
                </div>
              </div>
              {/* ------ year and grade section end ----- */}
              {/* ------ Institute section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="university"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Institute")}
                </label>
                <input
                  type="text"
                  className={`form-control fs-sm shadow-none ${
                    errors.institute ? "is-invalid" : ""
                  }`}
                  placeholder={t("Institute")}
                  value={educationData.institute}
                  onChange={(e) => {
                    setEducationData((prevData) => ({
                      ...prevData,
                      institute: e.target.value,
                    }));
                    if (errors.institute) {
                      setErrors((prev) => ({ ...prev, institute: "" }));
                    }
                  }}
                />
                {errors.institute && (
                  <div className="invalid-feedback">{errors.institute}</div>
                )}
              </div>
              {/* ------ Institute section end ----- */}

              {/* ------ add/edit button start ----- */}
              <div className="action">
                <button
                  type="submit"
                  className="btn btn-primary"
                  aria-label="Close"
                  onClick={
                    educationIndex != null
                      ? editEducationBlockHandler
                      : addEducationHandler
                  }
                >
                  {educationIndex != null
                    ? t("Update Education")
                    : t("Add Education")}
                </button>
              </div>
              {/* ------ add/edit button end ----- */}
            </form>
          </div>
          {/* ------ modal body section end ----- */}
        </div>
      </div>
    </div>
  );
};
export default AddEditEducationModal;
