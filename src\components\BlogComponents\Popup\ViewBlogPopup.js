import React from "react";
import { Link } from "react-router-dom";

const ViewBlogPopup = () => {
  return (
    <div
      className="offcanvas md offcanvas-end bg-white border-0"
      tabIndex="-1"
      id="blogDetails"
      aria-labelledby="blogDetailsLabel"
    >
      <div className="offcanvas-header p-4 pb-0">
        <button
          type="button"
          className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
        <Link to="add-new-blog.html" className="btn btn-primary">
          Edit
        </Link>
      </div>
      <div className="offcanvas-body p-4">
        <p className="fs-md fw-semibold text-primary mb-2">BLOG POST</p>
        <h2>The Truth About Finding Your First Engineering Job</h2>
        <ul className="social-icons d-flex flex-wrap gap-2">
          <li></li>
        </ul>
        <div className="rounded-10 overflow-hidden mb-3">
          <img
            src="assets/img/blog-image.png"
            alt="The Truth About Finding Your First Engineering Job"
            className="img-fluid"
          />
        </div>
        <p>
          There are many variations of passages of Lorem Ipsum available, but
          the majority have suffered alteration in some form, by injected
          humour, or randomised words which don't look even slightly believable.
          If you are going to use a passage of Lorem Ipsum, you need to be sure
          there isn't anything embarrassing hidden in the middle of text.
        </p>
        <p>
          All the Lorem Ipsum generators on the Internet tend to repeat
          predefined chunks as necessary, making this the first true generator
          on the Internet. It uses a dictionary of over 200 Latin words,
          combined with a handful of model sentence structures, to generate
          Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is
          therefore always free from repetition, injected humour, or
          non-characteristic words etc.
        </p>
      </div>
    </div>
  );
};

export default ViewBlogPopup;
