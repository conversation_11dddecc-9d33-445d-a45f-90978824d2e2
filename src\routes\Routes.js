/* eslint-disable */
import Administration from "pages/Administration/Administration";
import Ecosystem from "pages/Administration/Ecosystem/Ecosystem";
import ModuleList from "pages/Administration/ModuleList/ModuleList";
import ModuleRolePrivilege from "pages/Administration/ModuleRolePrivilege/ModuleRolePrivilege";
import ModuleRoles from "pages/Administration/ModuleRoles/ModuleRoles";
import Roles from "pages/Administration/Roles/Roles";
import Plans from "pages/Administration/Plans/Plans";
import AffiliateProposals from "pages/Affiliations/AffiliateProposals/AffiliateProposals";
import AffiliateRequests from "pages/Affiliations/AffiliateRequests/AffiliateRequests";
import BlogList from "pages/Blog/Manage/BlogList";
import AddUpdateBlog from "pages/Blog/Save/AddUpdateBlog";
import CrmContactDetailsRecievedMail from "pages/CRM/ContactDetailsTimeLine/Mail/CrmContactDetailsRecievedMail";
import CrmContactDetailsSentMail from "pages/CRM/ContactDetailsTimeLine/Mail/CrmContactDetailsSentMail";
import CrmContactDetailsTask from "pages/CRM/ContactDetailsTimeLine/Task/CrmContactDetailsTask";
import CrmContactDetailsTimeLine from "pages/CRM/ContactDetailsTimeLine/TimeLine/CrmContactDetailsTimeLine";
import CrmContactList from "pages/CRM/ContactList/CrmContactList";
import CrmConversationRecievedList from "pages/CRM/Conversation/CrmConversationRecievedList";
import CrmConversationSentList from "pages/CRM/Conversation/CrmConversationSentList";
import CRMLabel from "pages/CRM/Label/CrmLabel";
import CrmLeadAddTask from "pages/CRM/LeadInformation/AddTask/CrmLeadAddTask";
import CrmLeadCommunication from "pages/CRM/LeadInformation/Communication/CrmLeadCommunication";
import CrmLeadInformationDetails from "pages/CRM/LeadInformation/LeadInformationDeatails/CrmLeadInformationDetails";
import CrmLeadList from "pages/CRM/LeadList/Manage/CrmLeadList";
import SaveCrmLead from "pages/CRM/LeadList/Save/SaveCrmLead";
import CrmList from "pages/CRM/List/CrmList";
import CrmMailTemplate from "pages/CRM/MailTemplate/CrmMailTemplate";
import CrmProcess from "pages/CRM/ActivityProcess/CrmProcess";
import CrmSalesOrder from "pages/CRM/SalesOrder/CrmSalesOrder";
import JobInformation from "pages/Challenges/ChallengeDetails/JobInformation/JobInformation";
import HiringProcess from "pages/Challenges/ChallengeDetails/HiringProcess/HiringProcess";
import HiringTeam from "pages/Challenges/ChallengeDetails/HiringTeam/HiringTeam";
import ChallengeResponseList from "pages/Challenges/ChallengeDetails/ChallengeResponses/ChallengeResponseList/ChallengeResponseList";
import JobSurvey from "pages/Challenges/ChallengeDetails/JobSurvey/JobSurvey";
import ChallengeList from "pages/Challenges/ChallengeList/ChallengeList";
import CompaniesLanding from "pages/Companies/CompaniesLanding/CompaniesLanding";
import CompanyFAQ from "pages/Companies/CompanyDetails/CompanyFAQ/CompanyFAQ";
import CompanyGeneralInfo from "pages/Companies/CompanyDetails/CompanyGeneralInfo/CompanyGeneralInfo";
import CompanyTeamMembers from "pages/Companies/CompanyDetails/CompanyTeamMembers/CompanyTeamMembers";
import CouponCreate from "pages/Coupons/CouponCreate/CouponCreate";
import CouponList from "pages/Coupons/CouponList/CouponList";
import CourseCertification from "pages/Courses/CourseDetails/CourseCertification/CourseCertification";
import CourseDetail from "pages/Courses/CourseDetails/CourseDetail/CourseDetail";
import CourseFaq from "pages/Courses/CourseDetails/CourseFaq/CourseFaq";
import CourseLessonsDetailsContent from "pages/Courses/CourseDetails/CourseLessons/CourseLessonsDetails/CourseLessonsDetailsContent/CourseLessonsDetailsContent";
import CourseLessonsDetailsCustomerQueries from "pages/Courses/CourseDetails/CourseLessons/CourseLessonsDetails/CourseLessonsDetailsCustomerQueries/CourseLessonsDetailsCustomerQueries";
import CourseLessonsDetailsFAQ from "pages/Courses/CourseDetails/CourseLessons/CourseLessonsDetails/CourseLessonsDetailsFAQ/CourseLessonsDetailsFAQ";
import CourseLessonsDetailsTask from "pages/Courses/CourseDetails/CourseLessons/CourseLessonsDetails/CourseLessonsDetailsTask/CourseLessonsDetailsTask";
import CourseLessonsList from "pages/Courses/CourseDetails/CourseLessons/CourseLessonsList/CourseLessonsList";
import CourseSubscriber from "pages/Courses/CourseDetails/CourseSubscriber/CourseSubscriber";
import CourseTaskSubmitted from "pages/Courses/CourseDetails/CourseTaskSubmitted/CourseTaskSubmitted";
import CourseTeams from "pages/Courses/CourseDetails/CourseTeams/CourseTeams";
import CourseList from "pages/Courses/CourseList/CourseList";

import EventAgenda from "pages/Events/EventDetails/EventAgenda/EventAgenda";
import EventGallery from "pages/Events/EventDetails/EventGallery/EventGallery";
import EventInfo from "pages/Events/EventDetails/EventInfo/EventInfo";
import EventMembers from "pages/Events/EventDetails/EventMembers/EventMembers";
import EventPartner from "pages/Events/EventDetails/EventPartners/EventPartner";
import SubEvents from "pages/Events/EventDetails/SubEvents/SubEvents";
import EventsList from "pages/Events/EventsList/EventsList";
import EventQuotationsBuy from "pages/Events/EventDetails/EventQuotationsBuy/EventQuotationsBuy";
import EventQuotationsSell from "pages/Events/EventDetails/EventQuotationsSell/EventQuotationsSell";
import EventProducts from "pages/Events/EventDetails/EventProducts/EventProducts";
import QuotationBuyKanban from "pages/Events/EventDetails/EventQuotationsBuy/QuotationBuyKanbanKit";
import QuotationSellKanban from "pages/Events/EventDetails/EventQuotationsSell/QuotationSellKanbanKit";

import ForgotPassword from "pages/ForgotPassword/ForgotPassword";
import GroupCompaniesAdd from "pages/Groups/GroupDetails/GroupCompanies/GroupCompaniesAdd/GroupCompaniesAdd";
import GroupCompaniesList from "pages/Groups/GroupDetails/GroupCompanies/GroupCompaniesList/GroupCompaniesList";
import GroupAddEvent from "pages/Groups/GroupDetails/GroupEvent/GroupAddEvent/GroupAddEvent";
import GroupEventList from "pages/Groups/GroupDetails/GroupEvent/GroupEventList/GroupEventList";
import GroupGallery from "pages/Groups/GroupDetails/GroupGallery/GroupGallery";
import GroupInfo from "pages/Groups/GroupDetails/GroupInfo/GroupInfo";
import GroupMembers from "pages/Groups/GroupDetails/GroupMembers/GroupMembers";
import GroupProductAdd from "pages/Groups/GroupDetails/GroupProduct/GroupProductAdd/GroupProductAdd";
import GroupProductList from "pages/Groups/GroupDetails/GroupProduct/GroupProductList/GroupProductList";
import GroupProductImages from "pages/Groups/GroupDetails/GroupProduct/groupProductDetails/GroupProductImages/GroupProductImages";
import GroupProductOffers from "pages/Groups/GroupDetails/GroupProduct/groupProductDetails/GroupProductOffers/GroupProductOffers";
import GroupProductVariations from "pages/Groups/GroupDetails/GroupProduct/groupProductDetails/GroupProductVariations/GroupProductVariations";
import GroupProductVitalInfo from "pages/Groups/GroupDetails/GroupProduct/groupProductDetails/GroupProductVitalInfo/GroupProductVitalInfo";
import GroupProductWarranty from "pages/Groups/GroupDetails/GroupProduct/groupProductDetails/GroupProductWarranty/GroupProductWarranty";
import GroupList from "pages/Groups/GroupList/GroupList";
import AuthValidate from "pages/Login/AuthValidate";
import Login from "pages/Login/Login";
import Logout from "pages/Login/Logout";
import OfferList from "pages/Offers/OfferList/OfferList";
import OfferResponse from "pages/Offers/OfferResponse/OfferResponse";
import ProcessList from "pages/Processes/ProcessList";
import ProjectDetail from "pages/ProjectManagement/ProjectDetails/ProjectDetail/ProjectDetail";
import ProjectInvestments from "pages/ProjectManagement/ProjectDetails/ProjectInvestments/ProjectInvestments";
import ProjectKPIs from "pages/ProjectManagement/ProjectDetails/ProjectKPIs/ProjectKPIs";
import ProjectLogs from "pages/ProjectManagement/ProjectDetails/ProjectLogs/ProjectLogs";
import ProjectMembers from "pages/ProjectManagement/ProjectDetails/ProjectMembers/ProjectMembers";
import ProjectODS from "pages/ProjectManagement/ProjectDetails/ProjectODS/ProjectODS";
import ProjectReport from "pages/ProjectManagement/ProjectDetails/ProjectReport/ProjectReport";
import ProjectTasks from "pages/ProjectManagement/ProjectDetails/ProjectTasks/ProjectTasks";
import ProjectList from "pages/ProjectManagement/ProjectList/ProjectList";
import ProjectKanbanView from "pages/ProjectManagement/ProjectDetails/ProjectKanbanView/ProjectKanbanView";
import ProjectGanttView from "pages/ProjectManagement/ProjectDetails/ProjectGanttView/ProjectGanttView";

import Register from "pages/Register/Register";
import ResetPassword from "pages/ResetPassword/ResetPassword";
import SuccessStoriesList from "pages/SuccessStories/Manage/SuccessStoriesList";
import AddUpdateSuccessStory from "pages/SuccessStories/Save/AddUpdateSuccessStory";
import SurveyList from "pages/Survey/SurveyList/SurveyList";
import SurveyQuestion from "pages/Survey/SurveyQuestion/SurveyQuestion";
import SurveyResponse from "pages/Survey/SurveyResponse/SurveyResponse";
import VerifyEmail from "pages/VerifiyEmail/VerifyEmail";
import InvoiceList from "pages/Invoice/InvoiceList";
import InvoiceDetails from "pages/Invoice/InvoiceDetails";
import Payment from "pages/Payment/Payment";
import Subscription from "pages/Subscription/Subscription";

import MarketplaceProductList from "pages/Marketplace/Products/ProductList/ProductList";
import MarketplaceInventoryList from "pages/Marketplace/Inventory/InventoryList/InventoryList";
import MarketplaceOrdersList from "pages/Marketplace/Orders/OrderList/OrderLIst";
import MarketplaceInvoicesList from "pages/Marketplace/Invoices/InvoiceList/InvoiceList";
import MarketplaceSettings from "pages/Marketplace/Settings/Settings";

import MarketplaceProductInfo from "pages/Marketplace/Products/ProductDetails/VitalInfo/ProductInfo";
import MarketplaceProductCategory from "pages/Marketplace/Products/ProductDetails/ProductCategories/ProductCategories";
import MarketplaceProductVariations from "pages/Marketplace/Products/ProductDetails/ProductVariation/ProductVariations";
import MarketplaceProductOffers from "pages/Marketplace/Products/ProductDetails/ProductOffers/ProductOffers";
import MarketplaceProductGallery from "pages/Marketplace/Products/ProductDetails/ProductGallery/ProductGallery";

import AppUserList from "pages/AppUser/AppUserList/AppUserList";
import AppUserProfileInfo from "pages/AppUser/AppUserDetails/AppUserProfileInfo/AppUserProfileInfo";
import AppUserCv from "pages/AppUser/AppUserDetails/AppUserCv/AppUserCv";
import AppUserPrivacy from "pages/AppUser/AppUserDetails/AppUserPrivacy/AppUserPrivacy";
import AppUserPassword from "pages/AppUser/AppUserDetails/AppUserPassword/AppUserPassword";
import AppUserAvailibility from "pages/AppUser/AppUserDetails/AppUserAvailibility/AppUserAvailibility";
import ActivityProcessTemplate from "pages/Administration/ActivityProcessTemplate/ActivityProcessTemplate";
import Tickets from "pages/Tickets/Tickets";

import ChallengeResponseKanban from "pages/Challenges/ChallengeDetails/ChallengeResponses/ChallengeResponseKanban/ChallengeResponseKanban";
import UnSubscribeToMail from "pages/UnSubscribeToMail/UnSubscribeToMail";
import SubscribeToMail from "pages/SubscribeToMail/SubscribeToMail";
import SaveMailTemplate from "pages/CRM/MailTemplate/SaveMailTemplate";
import ProjectSectionTask from "pages/ProjectManagement/ProjectDetails/ProjectSectionTask/ProjectSectionTask";
import MyTaskList from "pages/MyTask/MyTaskList";
import MyTaskKanban from "pages/MyTask/MyTaskKanban";
import MyTaskGantt from "pages/MyTask/MyTaskGantt";
import AppUserSurveyInvites from "pages/AppUser/AppUserDetails/AppUserSurveyInvites/AppUserSurveyInvites";
import MailTemplateList from "pages/Administration/MailTemplates/MailTemplateList/MailTemplateList";
import SaveMailTemplateDetails from "pages/Administration/MailTemplates/MailTemplateDetails/SaveMailTemplateDetails";
import ActionLogs from "pages/Administration/ActionLogs/ActionLogs";
import GptPrompt from "pages/Administration/GptPormpt/GptPrompt";
import CrmLeadOpportunities from "pages/CRM/LeadInformation/Opportunities/CrmLeadOpportunities";
import Tags from "pages/Administration/Tags/Tags";
import CRMContactOpportunities from "pages/CRM/ContactDetailsTimeLine/Opportunities/CRMContactOpportunities";
import CrmLeadOpportunityKanban from "pages/CRM/LeadInformation/Opportunities/CrmLeadOpportunityKanban";
import CRMContactOpportunityKanban from "pages/CRM/ContactDetailsTimeLine/Opportunities/CRMContactOpportunityKanban";
import CrmOpportunies from "pages/CRM/Opportunities/CrmOpportunies";

import CrmOpportunityKanban from "pages/CRM/Opportunities/CrmOpportunityKanban";
import CrmOpportunityKanbanKit from "pages/CRM/Opportunities/CrmOpportunityKanbanKit";

import TaskDetails from "pages/TaskDetails/TaskDetails";
import SaveLeadOpportunities from "../pages/CRM/LeadInformation/Opportunities/SaveLeadOpportunities";
import SaveContactOpportunities from "pages/CRM/ContactDetailsTimeLine/Opportunities/SaveContactOpportunities";
import SaveOpportunities from "pages/CRM/Opportunities/SaveOpportunities";
import EcoLeadList from "pages/CRM/EcoLeads/EcoLeadList";
import SaveProcessTemplate from "pages/Administration/ActivityProcessTemplate/SaveProcessTemplate";
import SaveProcess from "pages/CRM/ActivityProcess/SaveProcess";
import CopyProject from "pages/ProjectManagement/ProjectDetails/CopyProject/CopyProject";
import AppUserSettings from "pages/AppUser/AppUserDetails/AppUserSettings/AppUserSettings";
import UserSurvey from "pages/UserSurvey/UserSurvey";
import AvailabilityCalendar from "pages/AvailabilityCalendar/AvailabilityCalendar";
import CustomerJobFeedback from "pages/CustomerJobFeedback/CustomerJobFeedback";
import CourseChapters from "pages/Courses/CourseDetails/CourseChapters/CourseChapters";

import GuestPayment from "pages/Payment/GuestPayment";
import SuccessPayment from "pages/Payment/SuccessPayment";
import FailurePayment from "pages/Payment/FailurePayment";
import CrmNotes from "pages/CRM/Notes/CrmNotes";
import SurveyInvite from "pages/Survey/SurveyInvite/SurveyInvite";
import SurveyFeedback from "pages/SurveyFeedback/SurveyFeedback";
import ProjectManagementDashboard from "pages/ProjectManagement/Dashboard/ProjectManagementDashboard";
import AllProjectMemberList from "pages/ProjectManagement/MemberList/AllProjectMemberList";
import ProjectGraphView from "pages/ProjectManagement/FullReport/Graph/ProjectGraphView";
import ProjectManagementList from "pages/ProjectManagement/FullReport/List/ProjectManagementList";
import ProjectManagementKanbanView from "pages/ProjectManagement/FullReport/Kanban/ProjectManagementKanbanView";
import CrmLeadKanban from "pages/CRM/LeadList/Kanban/CrmLeadKanban";
import CrmLeadKanbanKit from "pages/CRM/LeadList/Kanban/CrmLeadKanbanKit";
import CrmLeadDashboard from "pages/CRM/LeadList/Dashboard/CrmLeadDashboard";
import DirectAccess from "pages/DirectAccess/DirectAccess";
import AllLogList from "pages/AllLogs/AllLogList/AllLogList";
import AllLogKanban from "pages/AllLogs/AllLogKanban/AllLogKanban";

import ChallengeDashboard from "pages/Challenges/Dashboard/ChallengeDashboard";
import ChallengeGlobalReportList from "pages/Challenges/FullReport/List/GlobalReportList";
// import ChallengeResponsesKanbanView from "pages/Challenges/FullReport/Kanban/ChallengeResponsesKanbanView";
import ChallengeResponseDetails from "pages/Challenges/ChallengeDetails/ChallengeResponses/ChallengeResponseDetails/ChallengeResponseDetails";
import InvoiceDocList from "pages/InvoiceDoc/InvoiceDocList";
import InvoiceDocDetails from "pages/InvoiceDoc/InvoiceDocDetails";
import CRMLeadAddToChallenge from "pages/CRM/LeadInformation/AddToChallenge/CRMLeadAddToChallenge";
import CRMLeadCVBuilder from "pages/CRM/LeadInformation/LeadCvBuilder/CRMLeadCVBuilder";

// Dashboards
// import Dashboard from "pages/Dashboard/Dashboard";
import GlobalDashboard from "pages/Dashboard/GlobalDashboard";
import DashboardOld from "pages/Dashboard/DashboardOld";
import TicketDashboard from "pages/TicketDashboard/TicketDashboard";
import CrmConversationSentReport from "pages/CRM/Conversation/CrmConversationSentReport";
import FaqList from "pages/Faq/FaqList";
import ProjectTypeTaxonomy from "pages/Administration/TaxonomyTypes/ProjectType/ProjectTypeTaxonomy";
import ProjectTaxonomies from "pages/ProjectManagement/ProjectDetails/ProjectTaxonomies/ProjectTaxonomies";
import CategoryList from "pages/Administration/Categories/CategoryList";
import SaveCategory from "pages/Administration/Categories/SaveCategory";
import ProjectTemplates from "pages/Administration/ProjectTemplates/ProjectTemplates";
import SurveyFeedbackByCode from "pages/SurveyFeedback/SurveyFeedbackByCode";
import TaskWorking from "pages/TaskWorking/TaskWorking";
import MyTaskKanbanKit from "pages/MyTask/MyTaskKanbanKit";
// import DigitalKitDashboard from "pages/Dashboard/DigitalKitDashboard";

import AutomationList from "pages/Automation/AutomationList/AutomationList";
import AutomationDetails from "pages/Automation/AutomationDetails/AutomationDetails";
import AutomationSave from "pages/Automation/AutomationSave/AutomationSave";
import AutomationEventsList from "pages/Automation/AutomationEventsList/AutomationEventsList";
import LeadTimeline from "pages/CRM/LeadInformation/Timeline/LeadTimeline";
import EventFeedbackRules from "pages/Events/EventDetails/EventFeedback/FeedbackRules/EventFeedbackRules";
import EventFeedbackGiven from "pages/Events/EventDetails/EventFeedback/FeedbackGiven/EventFeedbackGiven";

/*======= public route start =======*/
const publicRoutes = [
  { path: "/", component: Login }, //login page,
  { path: "/auth/validate", component: AuthValidate }, //validate page
  { path: "/admin/register", component: Register }, //register page
  { path: "/admin/forgotpassword", component: ForgotPassword }, //forgot passowrd page
  { path: "/admin/verifyemail", component: VerifyEmail }, //verify email page
  { path: "/admin/resetpassword", component: ResetPassword }, //reset passowrd page
  { path: "/unsubscribetomail/:id", component: UnSubscribeToMail }, //unsubscribe mail page
  { path: "/subscribetomail/:id", component: SubscribeToMail }, //subscribe mail page
  { path: "/admin/customerfeedback/:id", component: CustomerJobFeedback }, //subscribe mail page
  { path: "/auth/payment/guest", component: GuestPayment }, //guest payment on subscription buy from other platform
  { path: "/auth/payment/success", component: SuccessPayment },
  { path: "/auth/payment/failure", component: FailurePayment },
  //user survey for another component
  {
    path: "/admin/survey/invite/:id",
    component: UserSurvey,
  },

  //guest/user survey
  {
    path: "/admin/survey/feedback/:id",
    component: SurveyFeedback,
  },

  //user survey for another component
  {
    path: "/survey/:codeslug",
    component: SurveyFeedbackByCode,
  },
];
/*======= public route end =======*/

/*======= protected route start =======*/
const protectedRoutes = [
  /* ========== logout start ======== */
  {
    path: "/logout",
    component: Logout,
  },
  /* ========== logout end ======== */

  { path: "/unsubscribetomail/:id", component: UnSubscribeToMail }, //unsubscribe mail page
  { path: "/subscribetomail/:id", component: SubscribeToMail }, //subscribe mail page
  { path: "/admin/customerfeedback/:id", component: CustomerJobFeedback }, //subscribe mail page
  { path: "/auth/payment/guest", component: GuestPayment }, //guest payment on subscription buy from other platform
  { path: "/auth/payment/success", component: SuccessPayment },
  { path: "/auth/payment/failure", component: FailurePayment },
  /* ======== validation start ======== */
  { path: "/auth/validate", component: AuthValidate }, //validate page
  /* ======== validation end ======== */

  /* ======== dashboard start ========== */
  {
    path: "/admin/dashboard",
    component: GlobalDashboard,
  },
  // {
  //   path: "/admin/dashboard/kitdigital",
  //   component: DigitalKitDashboard,
  // },
  {
    path: "/admin/olddashboard",
    component: DashboardOld,
  },
  {
    path: "/admin/ticketdashboard",
    component: TicketDashboard,
  },
  /* ======== dashboard end ========== */

  /* ======== administration start ========== */
  {
    path: "/admin/administration",
    component: Administration,
  },
  /* ======== administration end ========== */

  /* ======== administration start ========== */
  {
    path: "/admin/administration/ecosystems",
    component: Ecosystem,
  },
  /* ======== administration end ========== */

  /* ======== categories start ========== */
  {
    path: "/admin/administration/categories/list",
    component: CategoryList,
  },
  {
    path: "/admin/administration/categories/save",
    component: SaveCategory,
  },
  {
    path: "/admin/administration/categories/save/:id",
    component: SaveCategory,
  },
  /* ======== categories end ========== */

  /* ======== roles start ========== */
  {
    path: "/admin/administration/roles",
    component: Roles,
  },
  /* ======== roles end ========== */

  /* ======== plans start ========== */
  {
    path: "/admin/administration/plans",
    component: Plans,
  },
  /* ======== plans end ========== */

  /* ======== module start ========== */
  {
    path: "/admin/administration/modules",
    component: ModuleList,
  },
  /* ======== module end ========== */

  /* ======== module roles start ========== */
  {
    path: "/admin/administration/moduleroles",
    component: ModuleRoles,
  },
  /* ======== module roles end ========== */

  /* ======== module role previlege start ========== */
  {
    path: "/admin/administration/moduleroleprivilege",
    component: ModuleRolePrivilege,
  },
  /* ======== module role previlege end ========== */

  /* ======== activity process start ========== */
  {
    path: "/admin/administration/activityprocess/list",
    component: ActivityProcessTemplate,
  },
  {
    path: "/admin/administration/activityprocess/save",
    component: SaveProcessTemplate,
  },
  {
    path: "/admin/administration/activityprocess/save/:id",
    component: SaveProcessTemplate,
  },
  /* ======== activity process end ========== */

  /* ======== mail template start ========== */
  {
    path: "/admin/administration/mailtemplate/list",
    component: MailTemplateList,
  },
  {
    path: "/admin/administration/mailtemplate/save",
    component: SaveMailTemplateDetails,
  },
  {
    path: "/admin/administration/mailtemplate/save/:id",
    component: SaveMailTemplateDetails,
  },
  /* ======== mail template end ========== */
  // action logs
  {
    path: "/admin/administration/actionlogs",
    component: ActionLogs,
  },

  {
    path: "/admin/administration/gptprompt",
    component: GptPrompt,
  },

  {
    path: "/admin/administration/tags",
    component: Tags,
  },

  /**====== taxonomy start =======*/
  {
    path: "/admin/administration/taxonomytype/project",
    component: ProjectTypeTaxonomy,
  },
  /**====== taxonomy end =======*/

  /* ======== project template start ========== */
  {
    path: "/admin/administration/projecttemplate/list",
    component: ProjectTemplates,
  },
  // {
  //   path: "/admin/administration/projecttemplate/save",
  //   component: SaveMailTemplateDetails,
  // },
  // {
  //   path: "/admin/administration/projecttemplate/save/:id",
  //   component: SaveMailTemplateDetails,
  // },
  /* ======== project template end ========== */

  /* ======== calendar start ========== */
  {
    path: "/admin/availabilitycalendar",
    component: AvailabilityCalendar,
  },
  /* ======== calendar end ========== */

  /* ======== Direct Access Start  ========== */
  {
    path: "/admin/directaccess",
    component: DirectAccess,
  },
  /* ======== Direct Access End  ========== */

  /* ======== crm start ======= */
  {
    path: "/admin/crm/ecolead/list",
    component: EcoLeadList,
  },
  {
    path: "/admin/crm/lead/list",
    component: CrmLeadList,
  },
  {
    path: "/admin/crm/lead/kanban",
    component: CrmLeadKanban,
  },
  {
    path: "/admin/crm/lead/kanbankit",
    component: CrmLeadKanbanKit,
  },
  {
    path: "/admin/crm/lead/dashboard",
    component: CrmLeadDashboard,
  },
  {
    path: "/admin/crm/lead/information/:id",
    component: CrmLeadInformationDetails,
  },
  {
    path: "/admin/crm/lead/timeline/:id",
    component: LeadTimeline,
  },
  {
    path: "/admin/crm/lead/addtask/:id",
    component: CrmLeadAddTask,
  },
  {
    path: "/admin/crm/lead/communication/:id",
    component: CrmLeadCommunication,
  },
  {
    path: "/admin/crm/lead/opportunities/:id",
    component: CrmLeadOpportunities,
  },
  {
    path: "/admin/crm/lead/opportunity/kanban/:id",
    component: CrmLeadOpportunityKanban,
  },
  {
    path: "/admin/crm/lead/save",
    component: SaveCrmLead,
  },
  {
    path: "/admin/crm/lead/save/:id",
    component: SaveCrmLead,
  },
  {
    path: "/admin/crm/lead/opportunity/save/:id",
    component: SaveLeadOpportunities,
  },
  {
    path: "/admin/crm/lead/opportunity/save/:id/:invoiceid",
    component: SaveLeadOpportunities,
  },
  {
    path: "/admin/crm/lead/addtochallenge/:id",
    component: CRMLeadAddToChallenge,
  },
  {
    path: "/admin/crm/lead/cvbuilder/:id",
    component: CRMLeadCVBuilder,
  },

  {
    path: "/admin/crm/contact/list",
    component: CrmContactList,
  },
  {
    path: "/admin/crm/contact/details/timeline/:id",
    component: CrmContactDetailsTimeLine,
  },
  {
    path: "/admin/crm/contact/details/task/:id",
    component: CrmContactDetailsTask,
  },
  {
    path: "/admin/crm/contact/details/recievedmail/:id",
    component: CrmContactDetailsRecievedMail,
  },
  {
    path: "/admin/crm/contact/details/sentmail/:id",
    component: CrmContactDetailsSentMail,
  },
  {
    path: "/admin/crm/contact/details/opportunities/:id",
    component: CRMContactOpportunities,
  },
  {
    path: "/admin/crm/contact/details/opportunity/kanban/:id",
    component: CRMContactOpportunityKanban,
  },
  {
    path: "/admin/crm/contact/opportunity/save/:id",
    component: SaveContactOpportunities,
  },
  {
    path: "/admin/crm/contact/opportunity/save/:id/:invoiceid",
    component: SaveContactOpportunities,
  },
  {
    path: "/admin/crm/conversation/recieved",
    component: CrmConversationRecievedList,
  },
  {
    path: "/admin/crm/conversation/sent",
    component: CrmConversationSentList,
  },
  {
    path: "/admin/crm/conversation/sentreport",
    component: CrmConversationSentReport,
  },
  {
    path: "/admin/crm/list",
    component: CrmList,
  },
  {
    path: "/admin/crm/mailtemplate/list",
    component: CrmMailTemplate,
  },
  {
    path: "/admin/crm/mailtemplate/save",
    component: SaveMailTemplate,
  },
  {
    path: "/admin/crm/mailtemplate/save/:id",
    component: SaveMailTemplate,
  },
  {
    path: "/admin/crm/label/list",
    component: CRMLabel,
  },
  {
    path: "/admin/crm/process/list",
    component: CrmProcess,
  },
  {
    path: "/admin/crm/process/save",
    component: SaveProcess,
  },
  {
    path: "/admin/crm/process/save/:id",
    component: SaveProcess,
  },
  {
    path: "/admin/crm/salesorder/list",
    component: CrmSalesOrder,
  },
  {
    path: "/admin/crm/opportunity/list",
    component: CrmOpportunies,
  },
  {
    path: "/admin/crm/opportunity/kanban",
    component: CrmOpportunityKanban,
  },
  {
    path: "/admin/crm/opportunity/kanbankit",
    component: CrmOpportunityKanbanKit,
  },
  // {
  //   path: "/admin/crm/opportunity/kanban",
  //   component: CrmOpportunityKanbanKit,
  // },
  {
    path: "/admin/crm/opportunity/save/:invoiceid",
    component: SaveOpportunities,
  },
  {
    path: "/admin/crm/note/list",
    component: CrmNotes,
  },
  /* ======== crm end ======= */

  /* ===== events start ======== */
  {
    path: "/admin/events/list",
    component: EventsList,
  },
  {
    path: "/admin/events/info",
    component: EventInfo,
  },
  {
    path: "/admin/events/info/:id",
    component: EventInfo,
  },
  {
    path: "/admin/events/subevents/:id",
    component: SubEvents,
  },
  {
    path: "/admin/events/members/:id",
    component: EventMembers,
  },
  {
    path: "/admin/events/eventpartner/:id",
    component: EventPartner,
  },
  {
    path: "/admin/events/agenda/:id",
    component: EventAgenda,
  },
  {
    path: "/admin/events/gallery/:id",
    component: EventGallery,
  },
  {
    path: "/admin/events/feedbackrules/:id",
    component: EventFeedbackRules,
  },
  {
    path: "/admin/events/feedbackgiven/:id",
    component: EventFeedbackGiven,
  },
  {
    path: "/admin/events/products/:id",
    component: EventProducts,
  },
  {
    path: "/admin/events/quotations/buy/:id",
    component: EventQuotationsBuy,
  },
  {
    path: "/admin/events/quotations/sell/:id",
    component: EventQuotationsSell,
  },
  {
    path: "/admin/events/quotations/buy/kanbankit/:id", // kanban view by event id
    component: QuotationBuyKanban,
  },
  {
    path: "/admin/events/quotations/sell/kanbankit/:id", // kanban view by event id
    component: QuotationSellKanban,
  },

  /* ===== events end ======== */

  /* ====== blogs start ======== */
  {
    path: "/admin/blogs/list",
    component: BlogList,
  },
  {
    path: "/admin/blog/save",
    component: AddUpdateBlog,
  },
  {
    path: "/admin/blog/save/:id",
    component: AddUpdateBlog,
  },
  /* ====== blogs end ======== */

  /*====== success stories start ======*/
  {
    path: "/admin/successstories/list",
    component: SuccessStoriesList,
  },
  {
    path: "/admin/successstories/save",
    component: AddUpdateSuccessStory,
  },
  {
    path: "/admin/successstories/save/:id",
    component: AddUpdateSuccessStory,
  },
  /*====== success stories end ======*/

  /* ======= survey start ====== */
  {
    path: "/admin/survey/list",
    component: SurveyList,
  },
  //:id means survey id
  {
    path: "/admin/survey/question/list/:id",
    component: SurveyQuestion,
  },
  {
    path: "/admin/survey/invite/list/:id",
    component: SurveyInvite,
  },
  {
    path: "/admin/survey/response/list/:id",
    component: SurveyResponse,
  },

  //user survey for another component
  {
    path: "/admin/survey/invite/:id",
    component: UserSurvey,
  },

  //guest/user survey
  {
    path: "/admin/survey/feedback/:id",
    component: SurveyFeedback,
  },

  //user survey for another component
  {
    path: "/survey/:codeslug",
    component: SurveyFeedbackByCode,
  },
  /* ======= survey end ====== */

  /* ======= group start ========== */
  {
    path: "/admin/group/list",
    component: GroupList,
  },
  {
    path: "/admin/group/info",
    component: GroupInfo,
  },
  {
    path: "/admin/group/info/:id",
    component: GroupInfo,
  },
  {
    path: "/admin/group/members/:id",
    component: GroupMembers,
  },
  {
    path: "/admin/group/gallery/:id",
    component: GroupGallery,
  },
  {
    path: "/admin/group/product/list/:id",
    component: GroupProductList,
  },

  {
    path: "/admin/group/product/add/:id",
    component: GroupProductAdd,
  },
  {
    path: "/admin/group/product/vital-info/:id",
    component: GroupProductVitalInfo,
  },
  {
    path: "/admin/group/product/variations/:id",
    component: GroupProductVariations,
  },
  {
    path: "/admin/group/product/offers/:id",
    component: GroupProductOffers,
  },
  {
    path: "/admin/group/product/images/:id",
    component: GroupProductImages,
  },
  {
    path: "/admin/group/product/warranty/:id",
    component: GroupProductWarranty,
  },
  {
    path: "/admin/group/companies/:id",
    component: GroupCompaniesList,
  },
  {
    path: "/admin/group/companies/add/:id",
    component: GroupCompaniesAdd,
  },
  {
    path: "/admin/group/events/:id",
    component: GroupEventList,
  },
  {
    path: "/admin/group/events/add/:id",
    component: GroupAddEvent,
  },
  /* ======= group end ========== */

  /* ========= App user start ========== */
  {
    path: "/admin/appuser/list",
    component: AppUserList,
  },

  {
    path: "/admin/appuser/profile",
    component: AppUserProfileInfo,
  },
  {
    path: "/admin/appuser/profile/:id",
    component: AppUserProfileInfo,
  },
  {
    path: "/admin/appuser/cv/:id",
    component: AppUserCv,
  },
  {
    path: "/admin/appuser/surveyinvites/:id",
    component: AppUserSurveyInvites,
  },
  // {
  //   path: "/admin/appuser/courses/:id",
  //   component: ExpertDetailsCourses,
  // },
  // {
  //   path: "/admin/appuser/certification/:id",
  //   component: ExpertDetailsCertification,
  // },
  // {
  //   path: "/admin/appuser/ecoins/:id",
  //   component: ExpertDetailsEcoins,
  // },
  {
    path: "/admin/appuser/privacy/:id",
    component: AppUserPrivacy,
  },
  {
    path: "/admin/appuser/password/:id",
    component: AppUserPassword,
  },
  {
    path: "/admin/appuser/availability/:id",
    component: AppUserAvailibility,
  },
  {
    path: "/admin/appuser/settings/:id",
    component: AppUserSettings,
  },
  /* ========= experts end ========== */

  /* ========= companies start ============ */
  {
    path: "/admin/companies",
    component: CompaniesLanding,
  },
  {
    path: "/admin/companies/info",
    component: CompanyGeneralInfo,
  },
  {
    path: "/admin/companies/info/:id",
    component: CompanyGeneralInfo,
  },
  {
    path: "/admin/companies/teammembers/:id",
    component: CompanyTeamMembers,
  },
  {
    path: "/admin/companies/faq/:id",
    component: CompanyFAQ,
  },
  /* ========= companies end ============ */

  /* ========== project management start ============ */
  {
    path: "/admin/projectmanagement/dashboard",
    component: ProjectManagementDashboard,
  },
  {
    path: "/admin/projectmanagement/fullreport/graph",
    component: ProjectGraphView,
  },
  {
    path: "/admin/projectmanagement/fullreport/list",
    component: ProjectManagementList,
  },
  {
    path: "/admin/projectmanagement/fullreport/kanban",
    component: ProjectManagementKanbanView,
  },
  {
    path: "/admin/projectmanagement/allmembers",
    component: AllProjectMemberList,
  },
  {
    path: "/admin/projectmanagement/list",
    component: ProjectList,
  },
  {
    path: "/admin/projectmanagement/list/:userid",
    component: ProjectList,
  },
  {
    path: "/admin/projectmanagement/details",
    component: ProjectDetail,
  },
  {
    path: "/admin/projectmanagement/details/:id",
    component: ProjectDetail,
  },
  {
    path: "/admin/projectmanagement/copyproject/:id",
    component: CopyProject,
  },
  {
    path: "/admin/projectmanagement/taxonomies/:id",
    component: ProjectTaxonomies,
  },
  {
    path: "/admin/projectmanagement/tasksection/:id",
    component: ProjectSectionTask,
  },
  {
    path: "/admin/projectmanagement/tasklist/:id",
    component: ProjectTasks,
  },
  {
    path: "/admin/projectmanagement/kanban/:id",
    component: ProjectKanbanView,
  },
  {
    path: "/admin/projectmanagement/gantt/:id",
    component: ProjectGanttView,
  },
  {
    path: "/admin/projectmanagement/members/:id",
    component: ProjectMembers,
  },
  {
    path: "/admin/projectmanagement/logs/:id",
    component: ProjectLogs,
  },
  {
    path: "/admin/projectmanagement/ods/:id",
    component: ProjectODS,
  },
  {
    path: "/admin/projectmanagement/investment/:id",
    component: ProjectInvestments,
  },
  {
    path: "/admin/projectmanagement/kpis/:id",
    component: ProjectKPIs,
  },
  {
    path: "/admin/projectmanagement/report/:id",
    component: ProjectReport,
  },

  /* ========== project management end ============ */

  /* ========== my task start ============ */
  {
    path: "/admin/mytasklist",
    component: MyTaskList,
  },
  {
    path: "/admin/mytasklist/:userid",
    component: MyTaskList,
  },
  {
    path: "/admin/mytaskkanban",
    component: MyTaskKanban,
  },
  {
    path: "/admin/mytaskkanbankit",
    component: MyTaskKanbanKit,
  },
  {
    path: "/admin/mytaskkanban/:userid",
    component: MyTaskKanban,
  },
  {
    path: "/admin/mytaskkanbankit/:userid",
    component: MyTaskKanbanKit,
  },
  {
    path: "/admin/mytaskgantt",
    component: MyTaskGantt,
  },
  {
    path: "/admin/mytaskgantt/:userid",
    component: MyTaskGantt,
  },
  /* ========== my task end ============ */

  /*** ========== task working start ============ */
  {
    path: "/admin/workingtasks",
    component: TaskWorking,
  },
  /*** ========== task working end ============ */

  /* ========== all log start ============ */
  {
    path: "/admin/alllogs/list",
    component: AllLogList,
  },
  {
    path: "/admin/alllogs/kanban",
    component: AllLogKanban,
  },
  /* ========== all log end ============ */

  /* ========== courses start ========= */
  {
    path: "/admin/courses",
    component: CourseList,
  },
  {
    path: "/admin/course/save",
    component: CourseDetail,
  },
  {
    path: "/admin/course/save/:id",
    component: CourseDetail,
  },
  {
    path: "/admin/course/chapter/list/:id",
    component: CourseChapters,
  },
  {
    path: "/admin/course/lessons/list/:id",
    component: CourseLessonsList,
  },
  {
    path: "/admin/course/lessons/content/:id",
    component: CourseLessonsDetailsContent,
  },
  {
    path: "/admin/course/lessons/content/:id/:lessonid",
    component: CourseLessonsDetailsContent,
  },
  {
    path: "/admin/course/lessons/task/:id/:lessonid",
    component: CourseLessonsDetailsTask,
  },
  {
    path: "/admin/course/lessons/faq/:id/:lessonid",
    component: CourseLessonsDetailsFAQ,
  },
  {
    path: "/admin/course/lessons/customer-queries/:id/:lessonid",
    component: CourseLessonsDetailsCustomerQueries,
  },

  {
    path: "/admin/course/team/:id",
    component: CourseTeams,
  },
  {
    path: "/admin/course/subscribers/:id",
    component: CourseSubscriber,
  },
  {
    path: "/admin/course/task/list/:id",
    component: CourseTaskSubmitted,
  },
  {
    path: "/admin/course/certification/:id",
    component: CourseCertification,
  },
  {
    path: "/admin/course/faq/:id",
    component: CourseFaq,
  },

  /* ========== courses end ========= */

  /* ============ affliliations start ================ */
  {
    path: "/admin/affiliate-proposals",
    component: AffiliateProposals,
  },
  {
    path: "/admin/affiliate-requests",
    component: AffiliateRequests,
  },
  /* ============ affliliations end ================ */

  /* ============= challenges start =================== */
  {
    path: "/admin/challenges/dashboard",
    component: ChallengeDashboard,
  },
  {
    path: "/admin/challenges/globalreport",
    component: ChallengeGlobalReportList,
  },
  {
    path: "/admin/challenges/list",
    component: ChallengeList,
  },
  {
    path: "/admin/challenges/jobinformation",
    component: JobInformation,
  },
  {
    path: "/admin/challenges/jobinformation/:id",
    component: JobInformation,
  },
  {
    path: "/admin/challenges/hiringprocess/:id",
    component: HiringProcess,
  },
  {
    path: "/admin/challenges/hiringteam/:id",
    component: HiringTeam,
  },
  {
    path: "/admin/challenges/responses/list/:id",
    component: ChallengeResponseList,
  },
  {
    path: "/admin/challenges/responses/kanban/:id",
    component: ChallengeResponseKanban,
  },
  /* =============== challenge response details start ========================= */
  {
    path: "/admin/challengeresponse/details/:id/:rid", //rid means challenge response id
    component: ChallengeResponseDetails,
  },
  {
    path: "/admin/challenges/survey/:id",
    component: JobSurvey,
  },
  /* ============= challenges end =================== */

  /* ============== processes start ====================== */
  { path: "/admin/process", component: ProcessList },
  /* ============== processes end ====================== */

  /* ============ offers start ================ */
  { path: "/admin/offers/list", component: OfferList },
  { path: "/admin/offers/response", component: OfferResponse },
  /* ============ offers start ================ */

  /* =============== coupons start ========================= */
  { path: "/admin/coupons/list", component: CouponList },
  { path: "/admin/coupons/create", component: CouponCreate },
  /* =============== coupons end ========================= */

  /* =============== Invoice start ========================= */
  { path: "/admin/invoice/list", component: InvoiceList },
  {
    path: "/admin/invoice/details",
    component: InvoiceDetails,
  },
  {
    path: "/admin/invoice/details/:id",
    component: InvoiceDetails,
  },
  /* =============== invoice end ========================= */

  /* =============== invoice doc start ========================= */
  {
    path: "/admin/invoicedoc/list",
    component: InvoiceDocList,
  },
  {
    path: "/admin/invoicedoc/details/:id",
    component: InvoiceDocDetails,
  },
  /* =============== invoice doc end ========================= */

  /* =============== Payment start ========================= */
  { path: "/admin/payment/list", component: Payment },
  /* =============== payment end ========================= */

  /* =============== Subscriptions start ========================= */
  { path: "/admin/subscription/list", component: Subscription },
  /* =============== subscription end ========================= */

  /* ======= market place start =======*/
  //list
  {
    path: "/admin/marketplace/products/list",
    component: MarketplaceProductList,
  },

  {
    path: "/admin/marketplace/product/save",
    component: MarketplaceProductInfo,
  },

  {
    path: "/admin/marketplace/product/details/:id",
    component: MarketplaceProductInfo,
  },

  {
    path: "/admin/marketplace/product/category/:id",
    component: MarketplaceProductCategory,
  },

  {
    path: "/admin/marketplace/product/variants/:id",
    component: MarketplaceProductVariations,
  },

  {
    path: "/admin/marketplace/product/offers/:id",
    component: MarketplaceProductOffers,
  },

  {
    path: "/admin/marketplace/product/gallery/:id",
    component: MarketplaceProductGallery,
  },

  // {
  //   path: "/admin/marketplace/product/seo/:id",
  //   component: MarketplaceProductSEO,
  // }

  /* ======= marketplace invoice =======*/
  {
    path: "/admin/marketplace/invoices/list",
    component: MarketplaceInvoicesList,
  },

  /* ======= marketplace inventory =======*/
  {
    path: "/admin/marketplace/inventory/list",
    component: MarketplaceInventoryList,
  },

  /* ======= marketplace orders list =======*/
  {
    path: "/admin/marketplace/orders/list",
    component: MarketplaceOrdersList,
  },

  /* ======= marketplace settings =======*/
  {
    path: "/admin/marketplace/settings",
    component: MarketplaceSettings,
  },

  /* ======= market place end =======*/

  /* =============== task details start ========================= */
  { path: "/admin/task/details/:id", component: TaskDetails },
  /* =============== task details end ========================= */

  /* =============== Tickets start ========================= */
  { path: "/admin/tickets/list", component: Tickets },
  /* =============== Tickets end ========================= */

  /* =============== Faq start ========================= */
  { path: "/admin/faq/list", component: FaqList },
  /* =============== Faq end ========================= */

  /* =============== automation routes ========================= */
  {
    path: "/admin/automation/list",
    component: AutomationList,
  },
  {
    path: "/admin/automation/save",
    component: AutomationSave,
  },
  {
    path: "/admin/automation/save/:id",
    component: AutomationSave,
  },
  {
    path: "/admin/automation/details/:id",
    component: AutomationDetails,
  },
  {
    path: "/admin/automation/events/list",
    component: AutomationEventsList,
  },
];
/*======= protected route end =======*/

export { publicRoutes, protectedRoutes };
