import React, { useEffect } from "react";
import Header from "components/Common/Header/Header";
import AutomationSaveBody from "components/AutomationComponents/AutomationConfig/AutomationSave/AutomationSave";

const AutomationSave = () => {
  // -------- for title ------------
  useEffect(() => {
    document.title = "Automation Save";
  }, []);

  return (
    <main id="app">
      {/* ---- common header ---- */}
      <Header moduleName="automation" />

      <AutomationSaveBody />
    </main>
  );
};

export default AutomationSave;
