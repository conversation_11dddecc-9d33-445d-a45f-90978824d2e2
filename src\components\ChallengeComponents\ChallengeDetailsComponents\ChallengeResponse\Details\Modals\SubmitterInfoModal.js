/* eslint-disable */
import { useState, useEffect } from "react";

const SubmitterInfoModal = ({
  userName,
  setUserName,
  userEmail,
  setUserEmail,
  userPhone,
  setUserPhone,
  userPosition,
  setUserPosition,
}) => {
  // Initialize state with user information
  const [formData, setFormData] = useState({
    name: userName,
    email: userEmail,
    phone: userPhone,
    position: userPosition,
  });

  // Handle input changes
  useEffect(() => {
    setFormData({
      name: userName,
      email: userEmail,
      phone: userPhone,
      position: userPosition,
    });
  }, [userName, userEmail, userPhone, userPosition]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const resetHandler = () => {
    setFormData({
      name: userName,
      email: userEmail,
      phone: userPhone,
      position: userPosition,
    });
  };

  const saveInfoHandler = () => {
    setUserName(formData.name);
    setUserEmail(formData.email);
    setUserPhone(formData.phone);
    setUserPosition(formData.position);

    let modal = document.querySelector("#submitter_info_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="submitter_info_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Personal Information</h3>
                <h5>Add your contact details</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Full Name</label>
                    <input
                      type="text"
                      className="form-control"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div className="form-group">
                    <label>Email</label>
                    <input
                      type="email"
                      className="form-control"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Enter your email"
                    />
                  </div>
                  <div className="form-group">
                    <label>Phone</label>
                    <input
                      type="text"
                      className="form-control"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="Enter your phone number"
                    />
                  </div>
                  <div className="form-group">
                    <label>Position</label>
                    <input
                      type="text"
                      className="form-control"
                      name="position"
                      value={formData.position}
                      onChange={handleChange}
                      placeholder="Enter your position"
                    />
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmitterInfoModal;
