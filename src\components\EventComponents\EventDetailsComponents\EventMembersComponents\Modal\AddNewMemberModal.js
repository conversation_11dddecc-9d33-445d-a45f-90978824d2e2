/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
import Select from "react-select";
import CreatableSelect from "react-select/creatable";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

//import membership options from json
import EventMembershipStatusOption from "data/Prod/MembershipStatus.json";
import { useTranslation } from "react-i18next";

const AddNewMemberModal = ({
  roleList,
  afterCloseModalHandler,
  setShowAlert = () => {},
  setAlertMessage = () => {},
  setMessageType = () => {},
}) => {
  const { t } = useTranslation(); //for translation
  const params = useParams();

  const token = localStorage.getItem("token");

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [userInput, setUserInput] = useState("");
  const [userOptions, setuserOptions] = useState([]);
  const [userValue, setUserValue] = useState(null);
  const [userId, setUserId] = useState(null);
  const [userEmail, setUserEmail] = useState("");
  const [roleValue, setRoleValue] = useState(null);
  const [roleData, setRoleData] = useState(null);
  const [memberShipStatusValue, setMemberShipStatusValue] = useState(null);
  const [memberShipData, setmemberShipData] = useState(null);
  const [errorMessage, setErrorMessage] = useState("");

  const [isAdding, setIsAdding] = useState(false);

  /* hooks for validation */
  const [validation, setValidation] = useState({
    userWarning: false,
    roleWarning: false,
  });

  //** Function for Validation */
  const validationHandler = () => {
    let isValid = true;

    if (!userValue) {
      setValidation((prevState) => ({ ...prevState, userWarning: true }));
      isValid = false;
    }

    if (!roleValue) {
      setValidation((prevState) => ({ ...prevState, roleWarning: true }));
      isValid = false;
    }

    return isValid;
  };

  //function for get user details
  const getUserList = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USERLIST +
        `?token=${token}&userstringinput=${userInput}`;

      const response = await getData(requestUrl);

      if (response.status) {
        if (response.data.length > 0) {
          setuserOptions(response.data);
        }
        // else {
        //   const emailMatch = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        //   let userData = {
        //     label: userInput,
        //     value: userInput,
        //     email: emailMatch.test(userInput) ? userInput : "",
        //     _id: null,
        //   };
        //   setuserOptions([userData]);
        // }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get user input
  const userInputHandler = (val) => {
    setUserInput(val);
    setErrorMessage("");
  };

  //function user Selection
  const userSelectionHandler = (val) => {
    setErrorMessage("");
    if (val) {
      setUserValue(val);
      val._id ? setUserId(val._id) : setUserId(null); // if new user or other than contact user email is added
      val.email ? setUserEmail(val.email) : setUserEmail(val.value); // if new user or other than contact, set value as email
    } else {
      setUserValue(null);
      setUserId(null);
      setUserEmail("");
      setuserOptions([]);
    }
  };

  //function for select role
  const roleSelectionHandler = (val) => {
    if (val) {
      setRoleValue(val);
      setRoleData(val.value);
      setErrorMessage("");
    } else {
      setRoleValue(null);
      setRoleData(null);
    }

    //remove validation
    setValidation((prevState) => ({
      ...prevState,
      roleWarning: false,
    }));
  };

  //function for select memebership status
  const memberShipStatusSelectionHandler = (val) => {
    if (val) {
      setMemberShipStatusValue(val);
      setmemberShipData(val.value);
      setErrorMessage("");
    } else {
      setMemberShipStatusValue(null);
      setmemberShipData(null);
    }
  };

  //function for add event member
  const addEventMemberHandler = async () => {
    if (validationHandler()) {
      try {
        setIsAdding(true);

        let eventMemberData = {
          user: userId,
          event: params.id ? params?.id : null,
          role: roleData,
          useremail: userEmail,
          moderatorstatus: memberShipData,
          memberstatus: "0",
        };

        console.log("eventMemberData---->", eventMemberData);

        let requestUrl =
          url.API_BASE_URL + url.API_ADD_NEW_EVENT_MEMBER + `?token=${token}`;

        const response = await postData(requestUrl, eventMemberData);

        setIsAdding(false);

        console.log(response);

        if (response.status) {
          resetHandler();

          //hide member modal
          let bootstrapModal = document.querySelector("#addEventMember");
          let modal = bootstrap.Modal.getInstance(bootstrapModal);
          modal.hide();

          //call member list api function
          afterCloseModalHandler();
        } else {
          setErrorMessage(response.message);
        }
      } catch (error) {
        console.log(error.message);
      }
    } else {
      setMessageType("error");
      setAlertMessage("Please fill up the required fields");
      setErrorMessage(t("Please fill up the required fields"));
    }

    setShowAlert(true);
  };

  //function for reset
  const resetHandler = () => {
    setUserValue(null);
    setuserOptions([]);
    setMemberShipStatusValue(null);
    setmemberShipData(null);
    setRoleData(null);
    setRoleValue(null);
    setUserInput("");
    setUserId(null);
    setUserEmail("");
    setErrorMessage("");
    for (let memberStatus of EventMembershipStatusOption) {
      if (memberStatus.value === "1") {
        memberShipStatusSelectionHandler(memberStatus);
      }
    }

    setValidation({
      userWarning: false,
      roleWarning: false,
    });
  };

  useEffect(() => {
    if (userInput.length > 3) {
      getUserList();
    }
  }, [userInput]);

  useEffect(() => {
    for (let memberStatus of EventMembershipStatusOption) {
      if (memberStatus.value === "1") {
        memberShipStatusSelectionHandler(memberStatus);
      }
    }
  }, []);

  const optionsToShow = userOptions.length > 0 ? userOptions : [];
  return (
    <div
      className="modal fade"
      id="addEventMember"
      tabIndex="-1"
      aria-labelledby="addMemberLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content bg-white border-0 rounded-15">
          <div className="modal-header p-4 pb-0 border-0">
            <h2 className="fw-bold mb-0" id="addMemberLabel">
              {t("Add New Member")}
            </h2>
            <button
              type="button"
              className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
              data-bs-dismiss="modal"
              aria-label="Close"
              onClick={resetHandler}
            ></button>
          </div>
          <div className="modal-body p-4">
            <form onSubmit={(e) => e.preventDefault()}>
              {/* ----- user select section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="memberName"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Invite app users / guest (search by name, email)")}
                </label>

                <CreatableSelect
                  isClearable
                  className="w-100"
                  placeholder={t("Search by name, email")}
                  options={optionsToShow}
                  value={userValue}
                  onChange={(val) => {
                    userSelectionHandler(val);
                    setValidation((prevState) => ({
                      ...prevState,
                      userWarning: false,
                    }));
                    setErrorMessage("");
                  }}
                  onInputChange={(val) => {
                    userInputHandler(val);
                    setValidation((prevState) => ({
                      ...prevState,
                      userWarning: false,
                    }));
                    setErrorMessage("");
                  }}
                />
                {validation.userWarning && (
                  <div className="error-message mt-2">
                    <p className="d-flex align-items-center gap-1 text-danger">
                      <span className="material-symbols-outlined">warning</span>
                      <span>{t("Please enter a valid user")}!</span>
                    </p>
                  </div>
                )}
              </div>
              {/* ----- user select section end ----- */}

              {/* ----- role select section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="role"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Role")}
                </label>

                <Select
                  className="w-100"
                  placeholder={t("Select role")}
                  options={roleList}
                  value={roleValue}
                  onChange={(val) => roleSelectionHandler(val)}
                />

                {validation.roleWarning && (
                  <div className="error-message mt-2">
                    <p className="d-flex align-items-center gap-1 text-danger">
                      <span className="material-symbols-outlined">warning</span>
                      <span>{t("Please select a role")}!</span>
                    </p>
                  </div>
                )}
              </div>
              {/* ----- role select section end ----- */}

              {/* ----- status select section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="role"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Membership Status")}
                </label>

                <Select
                  className="w-100"
                  placeholder={t("Select status")}
                  options={EventMembershipStatusOption}
                  value={memberShipStatusValue}
                  onChange={(val) => memberShipStatusSelectionHandler(val)}
                />
              </div>
              {/* ----- status select section end ----- */}

              {/* ----- button section start ----- */}
              <div className="action d-flex align-items-center gap-3">
                <Link
                  to="#"
                  className="btn btn-outline-primary"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  onClick={resetHandler}
                >
                  {t("Cancel")}
                </Link>
                {errorMessage == "" ? null : (
                  <p className="text-danger">* {errorMessage}</p>
                )}
                {/* <Link to="#">
                  <span className="d-block material-symbols-outlined">
                    delete
                  </span>
                </Link> */}
                <button
                  type="button"
                  className="btn btn-primary ms-auto"
                  onClick={addEventMemberHandler}
                  disabled={isAdding ? true : false}
                  style={{
                    cursor: isAdding ? "not-allowed" : "pointer",
                  }}
                >
                  {t("Save")}
                  {isAdding && (
                    <div
                      className="mx-2 spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  )}
                </button>
              </div>
              {/* ----- button section end ----- */}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddNewMemberModal;
