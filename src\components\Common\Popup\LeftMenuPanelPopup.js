/* eslint-disable */
import { assetImages } from "constants";
import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";

const LeftMenuPanelPopup = ({ moduleName, userInfo }) => {
  const { t } = useTranslation(); // Translation hook
  const moduleAccess = localStorage.getItem("moduleaccess") || ""; // Get module access permissions
  const userRole = userInfo?.role?.slug || ""; // Extract user role

  // Construct user image URL with fallback
  const userImage = userInfo?.photoimage
    ? url.SERVER_URL + userInfo.photoimage.path
    : assetImages?.defaultUser;

  // Check if user is admin
  const isAdmin = ["ADMIN", "SUPER_ADMIN"].includes(userRole);

  // Check module access permissions
  const hasModuleAccess = (moduleKey) =>
    isAdmin || moduleAccess.includes(moduleKey);

  /**
   * Menu Configuration Array
   *
   * Structure:
   *   path: Route path
   *   icon: Material icon name
   *   label: Translation key
   *   module: Module identifier for active state
   *   show: Conditional display (true = always show, false = never show, function = conditional)
   *
   * Add new menu items here following the same structure
   */
  const menuItems = [
    // Always visible items
    {
      path: "/admin/dashboard",
      icon: "grid_view",
      label: "Dashboard",
      module: "dashboard",
    },
    {
      path: "/admin/directaccess",
      icon: "switch_access_2",
      label: "Direct Access",
      module: "directaccess",
    },

    // Conditional items
    {
      path: "/admin/administration",
      icon: "admin_panel_settings",
      label: "Administration",
      module: "administration",
      show: isAdmin,
    },
    {
      path: "/admin/appuser/list",
      icon: "person",
      label: "App Users",
      module: "appuser",
      show: hasModuleAccess("MOD_EXPERT"),
    },
    {
      path: "/admin/crm/lead/list",
      icon: "space_dashboard",
      label: "CRM",
      module: "crm",
      show: hasModuleAccess("MOD_CRM"),
    },
    {
      path: "/admin/companies",
      icon: "meeting_room",
      label: "Companies",
      module: "companies",
      show: hasModuleAccess("MOD_COMPANY"),
    },
    {
      path: "/admin/events/list",
      icon: "calendar_today",
      label: "Events",
      module: "events",
      show: hasModuleAccess("MOD_EVENT"),
    },
    {
      path: "/admin/group/list",
      icon: "diversity_2",
      label: "Groups",
      module: "group",
      show: hasModuleAccess("MOD_GROUP"),
    },
    {
      path: "/admin/survey/list",
      icon: "live_help",
      label: "Survey",
      module: "survey",
      show: hasModuleAccess("MOD_SURVEY"),
    },
    {
      path: "/admin/projectmanagement/dashboard",
      icon: "contract",
      label: "Project Management",
      module: "projects",
      show: hasModuleAccess("MOD_PROJECT"),
    },
    {
      path: "/admin/mytasklist",
      icon: "task",
      label: "Task Management",
      module: "mytask",
      show: hasModuleAccess("MOD_TASK"),
    },
    {
      path: "/admin/alllogs/list",
      icon: "timer",
      label: "Task Logs",
      module: "alllogs",
      show: hasModuleAccess("MOD_LOGS"),
    },
    {
      path: "/admin/workingtasks",
      icon: "task",
      label: "Task Working",
      module: "taskworking",
      show: hasModuleAccess("MOD_TASK"),
    },
    {
      path: "/admin/invoice/list",
      icon: "point_of_sale",
      label: "Invoices",
      module: "invoice",
      show: hasModuleAccess("MOD_INVOICE"),
    },
    {
      path: "/admin/invoicedoc/list",
      icon: "point_of_sale",
      label: "Invoice Docs",
      module: "invoicedoc",
      show: isAdmin,
    },
    {
      path: "/admin/payment/list",
      icon: "euro",
      label: "Payments",
      module: "payment",
      show: hasModuleAccess("MOD_PAYMENT"),
    },
    {
      path: "/admin/subscription/list",
      icon: "card_membership",
      label: "Subscription",
      module: "subscription",
      show: hasModuleAccess("MOD_SUBSCRIPTION"),
    },
    {
      path: "/admin/challenges/dashboard",
      icon: "work",
      label: "Challenges",
      module: "challenges",
      show: hasModuleAccess("MOD_CHALLENGE"),
    },
    {
      path: "/admin/courses",
      icon: "local_library",
      label: "Courses",
      module: "courses",
      show: hasModuleAccess("MOD_COURSE"),
    },
    {
      path: "/admin/marketplace/products/list",
      icon: "storefront",
      label: "Marketplace",
      module: "marketplace",
      show: hasModuleAccess("MOD_MARKETPLACE"),
    },
    {
      path: "/admin/tickets/list",
      icon: "transit_ticket",
      label: "Tickets",
      module: "tickets",
      show: hasModuleAccess("MOD_TICKET"),
    },
    {
      path: "/admin/blogs/list",
      icon: "article",
      label: "Blog",
      module: "blogs",
      show: hasModuleAccess("MOD_CMS"),
    },
    {
      path: "/admin/faq/list",
      icon: "quiz",
      label: "FAQ",
      module: "faq",
      show: hasModuleAccess("MOD_FAQ"),
    },
    {
      path: "/admin/automation/list",
      icon: "quiz",
      label: "Automation",
      module: "automation",
      show: hasModuleAccess("MOD_AUTOMATION"),
    },
  ];

  return (
    <div
      className="offcanvas offcanvas-start bg-white border-0"
      data-bs-scroll="true"
      data-bs-backdrop="true"
      tabIndex="-1"
      id="offcanvasMainNav"
      aria-labelledby="offcanvasMainNavLabel"
    >
      {/* ----- Header Section ----- */}
      <div className="offcanvas-header flex-column align-items-start body-bg p-4">
        {/* Close button */}{" "}
        <button
          type="button"
          className="btn-close p-0 bg-white rounded-circle shadow-none m-0"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        >
          <span className="d-block material-symbols-outlined icon-lg">
            arrow_back
          </span>
        </button>
        {/* User Profile */}
        <div className="user d-flex align-items-center gap-3 mt-4">
          <div className="avatar rounded-circle overflow-hidden">
            <img
              src={userImage}
              alt="UserImage"
              className="w-100 h-100 object-fit-cover object-center"
            />
          </div>
          <div className="name">
            <p className="fs-md fw-semibold mb-0">
              {userInfo.name} {userInfo.surname}
            </p>
            <p className="fs-xs lh-1 text-gray">{userInfo.position}</p>
          </div>
        </div>
      </div>

      {/* ----- Navigation Menu ----- */}
      <div className="offcanvas-body px-3 py-4">
        <ul className="nav flex-column gap-2 fs-md lh-1 fw-semibold">
          {/* 
            Dynamic Menu Rendering:
            - Filters menu items based on 'show' condition
            - Maps remaining items to Link components
            - Highlights active module based on current moduleName prop
          */}
          {menuItems.map((item) => {
            if (item.show === false) return null;
            return (
              <li className="nav-item" key={item.path}>
                <Link
                  to={item.path}
                  className={`d-flex align-items-center gap-3 text-black ${
                    moduleName === item.module ? "active" : ""
                  }`}
                  aria-current={moduleName === item.module ? "page" : undefined}
                >
                  <span className="d-block material-symbols-outlined">
                    {item.icon}
                  </span>
                  <span>{t(item.label)}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default LeftMenuPanelPopup;
