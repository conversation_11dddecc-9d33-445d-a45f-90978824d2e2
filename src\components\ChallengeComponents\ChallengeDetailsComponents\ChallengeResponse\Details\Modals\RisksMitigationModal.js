/* eslint-disable */
import { useState, useEffect } from "react";

const RisksMitigationModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  // Initialize formData with the data from capitalQuestResponseData
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  // Update formData whenever capitalQuestResponseData changes
  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      businessrisks: formData.businessrisks,
      mitigationstrategies: formData.mitigationstrategies,
      swotanalysis: formData.swotanalysis
    }));
    let modal = document.querySelector("#risks_mitigation_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="risks_mitigation_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Risks and Mitigation</h3>
                <h5>Provide details about business risks and mitigation strategies</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Business Risks</label>
                    <textarea
                      className="form-control"
                      name="businessrisks"
                      value={formData.businessrisks}
                      onChange={handleChange}
                      placeholder="Describe potential business risks"
                      rows="3"
                    />
                  </div>
                  <div className="form-group">
                    <label>Mitigation Strategies</label>
                    <textarea
                      className="form-control"
                      name="mitigationstrategies"
                      value={formData.mitigationstrategies}
                      onChange={handleChange}
                      placeholder="Describe your mitigation strategies"
                      rows="3"
                    />
                  </div>
                  <div className="form-group">
                    <label>SWOT Analysis</label>
                    <textarea
                      className="form-control"
                      name="swotanalysis"
                      value={formData.swotanalysis}
                      onChange={handleChange}
                      placeholder="Provide SWOT analysis"
                      rows="4"
                    />
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RisksMitigationModal;
