import React from "react";
import { useTranslation } from "react-i18next";
import { assetImages } from "constants";

const AccessDeniedView = () => {
  const { t } = useTranslation();
  return (
    <div id="content_wrapper">
      <section className="crm-wrapper bg-white pb-5">
        <div className="empty_access text-center">
          <div className="empty_pic mb-4">
            <img src={assetImages.emptyVector} alt="No access" />
          </div>
          <div className="empty_text">
            <p className="fs-lg text-gray fw-semibold mb-4">
              {t("Sorry....! You don't have privilege to see this content")}
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AccessDeniedView;
