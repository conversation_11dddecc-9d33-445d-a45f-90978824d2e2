/* eslint-disable */
import React, { useState, useEffect } from "react";

const ExecutiveSummaryModal = ({
  capitalQuestResponseData,
  setCapitalQuestResponseData,
}) => {
  const [formData, setFormData] = useState({ 
    startupname: capitalQuestResponseData.startupname || "",
    startupwebsite: capitalQuestResponseData.startupwebsite || "",
    startupemail: capitalQuestResponseData.startupemail || "",
    startuptagline: capitalQuestResponseData.startuptagline || "",
    startupdescription: capitalQuestResponseData.startupdescription || "",
    problemstatement: capitalQuestResponseData.problemstatement || "",
    solution: capitalQuestResponseData.solution || "",
    uniquevalueproposition: capitalQuestResponseData.uniquevalueproposition || "",
    businessmodel: capitalQuestResponseData.businessmodel || "",
    currentstatus: capitalQuestResponseData.currentstatus || "",
  });

  // Update formData whenever capitalQuestResponseData changes
  useEffect(() => {
    setFormData({
      startupname: capitalQuestResponseData.startupname || "",
      startupwebsite: capitalQuestResponseData.startupwebsite || "",
      startupemail: capitalQuestResponseData.startupemail || "",
      startuptagline: capitalQuestResponseData.startuptagline || "",
      startupdescription: capitalQuestResponseData.startupdescription || "",
      problemstatement: capitalQuestResponseData.problemstatement || "",
      solution: capitalQuestResponseData.solution || "",
      uniquevalueproposition: capitalQuestResponseData.uniquevalueproposition || "",
      businessmodel: capitalQuestResponseData.businessmodel || "",
      currentstatus: capitalQuestResponseData.currentstatus || "",
    });
  }, [capitalQuestResponseData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const resetHandler = () => {
    setFormData({ 
      startupname: capitalQuestResponseData.startupname || "",
      startupwebsite: capitalQuestResponseData.startupwebsite || "",
      startupemail: capitalQuestResponseData.startupemail || "",
      startuptagline: capitalQuestResponseData.startuptagline || "",
      startupdescription: capitalQuestResponseData.startupdescription || "",
      problemstatement: capitalQuestResponseData.problemstatement || "",
      solution: capitalQuestResponseData.solution || "",
      uniquevalueproposition: capitalQuestResponseData.uniquevalueproposition || "",
      businessmodel: capitalQuestResponseData.businessmodel || "",
      currentstatus: capitalQuestResponseData.currentstatus || "",
    });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      startupname: formData.startupname,
      startupwebsite: formData.startupwebsite,
      startupemail: formData.startupemail,
      startuptagline: formData.startuptagline,
      startupdescription: formData.startupdescription,
      problemstatement: formData.problemstatement,
      solution: formData.solution,
      uniquevalueproposition: formData.uniquevalueproposition,
      businessmodel: formData.businessmodel,
      currentstatus: formData.currentstatus,
    }));
    
    let modal = document.querySelector("#executive_summary_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="executive_summary_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Executive Summary</h3>
                <h5>Provide details about your startup</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-symbols-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Startup Name</label>
                    <input
                      type="text"
                      className="form-control"
                      name="startupname"
                      value={formData.startupname || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Startup Name"
                    />
                  </div>
                  <div className="form-group">
                    <label>Startup Website</label>
                    <input
                      type="text"
                      className="form-control"
                      name="startupwebsite"
                      value={formData.startupwebsite || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Startup Website"
                    />
                  </div>
                  <div className="form-group">
                    <label>Startup Email</label>
                    <input
                      type="email"
                      className="form-control"
                      name="startupemail"
                      value={formData.startupemail || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Startup Email"
                    />
                  </div>
                  <div className="form-group">
                    <label>Startup Tagline</label>
                    <input
                      type="text"
                      className="form-control"
                      name="startuptagline"
                      value={formData.startuptagline || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Startup Tagline"
                    />
                  </div>
                  <div className="form-group">
                    <label>Startup Description</label>
                    <textarea
                      className="form-control"
                      name="startupdescription"
                      value={formData.startupdescription || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Startup Description"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Problem Statement</label>
                    <textarea
                      className="form-control"
                      name="problemstatement"
                      value={formData.problemstatement || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Problem Statement"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Solution</label>
                    <textarea
                      className="form-control"
                      name="solution"
                      value={formData.solution || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Solution"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Unique Value Proposition</label>
                    <textarea
                      className="form-control"
                      name="uniquevalueproposition"
                      value={formData.uniquevalueproposition || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Unique Value Proposition"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Business Model</label>
                    <textarea
                      className="form-control"
                      name="businessmodel"
                      value={formData.businessmodel || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Business Model"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Current Status</label>
                    <textarea
                      className="form-control"
                      name="currentstatus"
                      value={formData.currentstatus || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Current Status"
                    ></textarea>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExecutiveSummaryModal;
