/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import CreatableSelect from "react-select/creatable";

import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";
import { useTranslation } from "react-i18next";

const AddEditExperienceModal = ({
  // companyList,
  experienceIndex,
  experienceData,
  setExperienceData,
  experienceListBlock,
  setExperienceListBlock,
  setExperienceIndex,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const { t } = useTranslation(); //for translation

  const [companyInput, setcompanyInput] = useState("");
  const [companyList, setCompanyList] = useState([]);

  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!experienceData.position.trim()) {
      newErrors.position = t("Position is required");
    }
    if (
      (!experienceData.companyid ||
        Object.keys(experienceData.companyid).length === 0) &&
      !experienceData.company.trim()
    ) {
      newErrors.companyid = t("Company is required");
      newErrors.company = t("Company is required");
    }
    if (!experienceData.start.trim()) {
      newErrors.start = t("Start date is required");
    }
    if (!experienceData.end.trim()) {
      newErrors.end = t("End date is required");
    }

    if (!experienceData.ctc.trim()) {
      newErrors.ctc = t("CTC is required");
    }
    if (!experienceData.responsibilities.trim()) {
      newErrors.responsibilities = t("Responsibilities is required");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  //function for get all companies
  const getCompanyList = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_COMPANY_BY_SEARCH +
        `?token=${token}&roleslug=${userInfo.role.name}&filtername=${companyInput}`;

      console.log("url of company------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response in company------>", response);

      if (response.status) {
        setCompanyList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //select company
  const companySelectionHandler = (val) => {
    if (val) {
      setExperienceData((prevData) => ({
        ...prevData,
        companyid: val,
      }));
    } else {
      setExperienceData((prevData) => ({
        ...prevData,
        companyid: null,
      }));
    }

    // Clear company error when selection changes
    if (errors.company || errors.companyid) {
      setErrors((prev) => ({ ...prev, company: "", companyid: "" }));
    }
  };

  //function for add edcuation block
  const addExperienceHandler = () => {
    if (validateForm()) {
      setExperienceListBlock([...experienceListBlock, experienceData]);
      closeModalHandler();
    }
  };

  //function for edit education block
  const editExperienceBlockHandler = () => {
    if (validateForm()) {
      const updatedExperienceList = [...experienceListBlock];
      updatedExperienceList[experienceIndex] = experienceData;
      setExperienceListBlock(updatedExperienceList);
      closeModalHandler();
    }
  };

  //close modal handler
  const closeModalHandler = () => {
    setExperienceData({
      companyid: null,
      company: "",
      position: "",
      start: "",
      end: "",
      ctc: "",
      responsibilities: "",
    });
    setExperienceIndex(null);

    setErrors({});

    // close the modal of addLabel
    const bootstrapModal = document.querySelector("#addExperienceModal");
    const modal = bootstrap.Modal.getInstance(bootstrapModal);
    modal.hide();
  };

  useEffect(() => {
    if (companyInput.length > 1) {
      const timer = setTimeout(() => {
        getCompanyList();
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [companyInput]);

  // Custom styles for CreatableSelect
  const customStyles = {
    control: (base, state) => ({
      ...base,
      borderColor: errors.company || errors.companyid ? "#dc3545" : "#ced4da",
      "&:hover": {
        borderColor: errors.company || errors.companyid ? "#dc3545" : "#ced4da",
      },
      minHeight: "38px",
      boxShadow: "none",
    }),
    placeholder: (base) => ({
      ...base,
      fontSize: "0.875rem",
      color: "#6c757d",
    }),
    input: (base) => ({
      ...base,
      fontSize: "0.875rem",
    }),
    singleValue: (base) => ({
      ...base,
      fontSize: "0.875rem",
    }),
  };

  const companyOptionsToShow = companyList.length > 0 ? companyList : [];

  return (
    <div
      className="modal fade"
      id="addExperienceModal"
      tabIndex="-1"
      aria-labelledby="addEducationModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content bg-white border-0 rounded-15">
          {/* Modal Header */}
          <div className="modal-header p-4 pb-0 border-0">
            <h2 className="fw-bold mb-0" id="addEducationModalLabel">
              {experienceIndex != null
                ? t("Update Experience")
                : t("Add Experience")}
            </h2>
            <button
              type="button"
              className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
              aria-label="Close"
              onClick={closeModalHandler}
            ></button>
          </div>

          {/* Modal Body */}
          <div className="modal-body p-4">
            <form onSubmit={(e) => e.preventDefault()}>
              {/* Company Selection */}

              {experienceData.company === "" && (
                <div className="form-group mb-4">
                  <label className="d-block fs-sm fw-semibold mb-2">
                    {t("Select Company")}
                  </label>
                  <CreatableSelect
                    placeholder={t("Type Company name")}
                    isClearable
                    options={companyOptionsToShow}
                    value={experienceData.companyid}
                    onChange={companySelectionHandler}
                    onInputChange={setcompanyInput}
                    styles={customStyles}
                  />
                  {errors.companyid && (
                    <div className="invalid-feedback">{errors.companyid}</div>
                  )}
                </div>
              )}

              {/* Manual Company Input */}
              {!experienceData.companyid && (
                <div className="form-group mb-4">
                  <label className="d-block fs-sm fw-semibold mb-2">
                    {t("Other Company (* If not in the list)")}
                  </label>
                  <input
                    type="text"
                    className={`form-control fs-sm shadow-none ${
                      errors.company ? "is-invalid" : ""
                    }`}
                    placeholder={t("Company")}
                    value={experienceData.company}
                    onChange={(e) => {
                      setExperienceData((prevData) => ({
                        ...prevData,
                        company: e.target.value,
                      }));
                      if (errors.company) {
                        setErrors((prev) => ({
                          ...prev,
                          company: "",
                          companyid: "",
                        }));
                      }
                    }}
                  />
                  {errors.company && (
                    <div className="invalid-feedback">{errors.company}</div>
                  )}
                </div>
              )}

              {/* Position & CTC */}
              <div className="form-group mb-4">
                <div className="row">
                  {/* Position */}
                  <div className="col-6">
                    <label className="d-block fs-sm fw-semibold mb-2">
                      {t("Position")}
                    </label>
                    <input
                      type="text"
                      className={`form-control fs-sm shadow-none ${
                        errors.position ? "is-invalid" : ""
                      }`}
                      placeholder={t("Position")}
                      value={experienceData.position}
                      onChange={(e) => {
                        setExperienceData((prevData) => ({
                          ...prevData,
                          position: e.target.value,
                        }));
                        if (errors.position) {
                          setErrors((prev) => ({ ...prev, position: "" }));
                        }
                      }}
                    />
                    {errors.position && (
                      <div className="invalid-feedback">{errors.position}</div>
                    )}
                  </div>

                  {/* CTC */}
                  <div className="col-6">
                    <label className="d-block fs-sm fw-semibold mb-2">
                      {t("CTC")}
                    </label>
                    <input
                      type="number"
                      className={`form-control fs-sm shadow-none ${
                        errors.ctc ? "is-invalid" : ""
                      }`}
                      placeholder={t("CTC")}
                      value={experienceData.ctc}
                      onChange={(e) => {
                        setExperienceData((prevData) => ({
                          ...prevData,
                          ctc: e.target.value,
                        }));
                        if (errors.ctc) {
                          setErrors((prev) => ({ ...prev, ctc: "" }));
                        }
                      }}
                    />
                    {errors.ctc && (
                      <div className="invalid-feedback">{errors.ctc}</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Start & End Years */}
              <div className="form-group mb-4">
                <div className="row">
                  {/* Start Year */}
                  <div className="col-6">
                    <label className="d-block fs-sm fw-semibold mb-2">
                      {t("Start Year")}
                    </label>
                    <input
                      type="number"
                      className={`form-control fs-sm shadow-none ${
                        errors.start ? "is-invalid" : ""
                      }`}
                      placeholder={t("Start Year")}
                      value={experienceData.start}
                      onChange={(e) => {
                        setExperienceData((prevData) => ({
                          ...prevData,
                          start: e.target.value,
                        }));
                        if (errors.start) {
                          setErrors((prev) => ({ ...prev, start: "" }));
                        }
                      }}
                    />
                    {errors.start && (
                      <div className="invalid-feedback">{errors.start}</div>
                    )}
                  </div>

                  {/* End Year */}
                  <div className="col-6">
                    <label className="d-block fs-sm fw-semibold mb-2">
                      {t("To Year")}
                    </label>
                    <input
                      type="number"
                      className={`form-control fs-sm shadow-none ${
                        errors.end ? "is-invalid" : ""
                      }`}
                      placeholder={t("To Year")}
                      value={experienceData.end}
                      onChange={(e) => {
                        setExperienceData((prevData) => ({
                          ...prevData,
                          end: e.target.value,
                        }));
                        if (errors.end) {
                          setErrors((prev) => ({ ...prev, end: "" }));
                        }
                      }}
                    />
                    {errors.end && (
                      <div className="invalid-feedback">{errors.end}</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Responsibilities */}
              <div className="form-group mb-4">
                <label className="d-block fs-sm fw-semibold mb-2">
                  {t("Responsibilities")}
                </label>
                <textarea
                  rows="5"
                  className={`form-control fs-sm shadow-none ${
                    errors.responsibilities ? "is-invalid" : ""
                  }`}
                  placeholder={t("Responsibilities")}
                  value={experienceData.responsibilities}
                  onChange={(e) => {
                    setExperienceData((prevData) => ({
                      ...prevData,
                      responsibilities: e.target.value,
                    }));
                    if (errors.responsibilities) {
                      setErrors((prev) => ({ ...prev, responsibilities: "" }));
                    }
                  }}
                ></textarea>
                {errors.responsibilities && (
                  <div className="invalid-feedback">
                    {errors.responsibilities}
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <div className="action">
                <button
                  type="submit"
                  className="btn btn-primary"
                  onClick={
                    experienceIndex != null
                      ? editExperienceBlockHandler
                      : addExperienceHandler
                  }
                >
                  {experienceIndex != null
                    ? t("Update Experience")
                    : t("Add Experience")}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddEditExperienceModal;
