/* eslint-disable */
import { useEffect, useState } from "react";
import Select from "react-select"; // Import react-select for tag-like multi-select UI
import { useTranslation } from "react-i18next";

import { getData, postData, putData, deleteData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import { filterOperators } from "data/Filter/filterOperators"; // Import centralized operators
import { filterAdvanceSelectStyle } from "Config/Config";

const OpportunityAdvFilterPopup = ({
  setJsonFilterQuery,
  reloadList = () => {},
  moduleSlug = "",
  isFilterReset = false,
  setIsFilterReset = () => {},
}) => {
  const { t } = useTranslation();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const userObj = {
    label: `${userInfo.name ?? userInfo.email} ${userInfo.surname ?? ""}`,
    value: userInfo._id,
  };

  const ecosystemSlug = localStorage.getItem("ecosystemslug");

  const [selectedFieldValue, setSelectedFieldValue] = useState(null);
  const [selectedField, setSelectedField] = useState("");
  const [selectedOperator, setSelectedOperator] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [inputValue2, setInputValue2] = useState(""); // for "between" range
  const [filters, setFilters] = useState([]);
  const [editIndex, setEditIndex] = useState(null);
  const [dynamicOptions, setDynamicOptions] = useState([]); // State to hold dynamic options
  const [inputQuery, setInputQuery] = useState(""); // New state for search query
  const [debouncedQuery, setDebouncedQuery] = useState(""); // Debounced query

  const [savedFilters, setSavedFilters] = useState([]); // State to hold saved filter templates
  const [selectedFilterTemplate, setSelectedFilterTemplate] = useState(null); // State for selected filter template
  const [filterTemplateName, setFilterTemplateName] = useState(""); // State for filter template name
  const [isDefaultTemplate, setIsDefaultTemplate] = useState(false); // State for default template checkbox
  const [showSaveFilterSection, setShowSaveFilterSection] = useState(false); // State to toggle Save Filter section
  const [isLoadingSavedFilters, setIsLoadingSavedFilters] = useState(false);
  const [notification, setNotification] = useState(""); // notification message

  //function for get all tags
  const getAllTags = async (query = "") => {
    // console.log("getAllTags called");

    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_TAGS +
        `?token=${token}&filterlabelname=${query}`;

      const response = await getData(requestUrl);

      // console.log("response allTags", response);

      if (response.status) {
        const tagsData = response.data.map((tag) => ({
          value: tag.value,
          label: tag.label,
        }));

        // setAllTags(tagsData);
        return tagsData;
      }
    } catch (error) {
      console.log(error.message);
      return [];
    }
  };

  const getOwnersOptions = async (query = "") => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USERLIST_OPTIONS +
        `?token=${token}&userstringinput=${query}`;

      console.log("url of owner------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response in owner------>", response);

      if (response.status) {
        const apiData = response.data.map((item) => ({
          value: item.value,
          label: item.label,
        }));

        // Check if current user already exists in API results
        const userExists = apiData.some((item) => item.value === userObj.value);

        // Append current user ONLY if not found in API response
        return userExists ? apiData : [...apiData, userObj];
      }
      // Return current user as fallback if API returns no results
      return [userObj];
    } catch (error) {
      console.log(error.message);
      return [];
    }
  };

  const getFollowersOptions = async (query = "") => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USERLIST_OPTIONS +
        `?token=${token}&userstringinput=${query}`;

      console.log("url of follower------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response in follower------>", response);

      if (response.status) {
        const apiData = response.data.map((item) => ({
          value: item.value,
          label: item.label,
        }));

        // Check if current user already exists in API results
        const userExists = apiData.some((item) => item.value === userObj.value);

        // Append current user ONLY if not found in API response
        return userExists ? apiData : [...apiData, userObj];
      }
      // Return current user as fallback if API returns no results
      return [userObj];
    } catch (error) {
      console.log(error.message);
      return [];
    }
  };

  // Return the saved filters with key as value, label, conditions, isdefault
  const fetchSavedFilters = async () => {
    // Replace this with an API call to fetch saved filters
    // return [
    //     { value: "template1", label: "Template 1", conditions: [{ field: "email", operator: "contains", value: "example" }] },
    //     { value: "template2", label: "Template 2", conditions: [{ field: "name", operator: "equals", value: "John Doe" }] },
    // ];

    const moduleName = moduleSlug; // Example module name

    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_FILTER +
        `?token=${token}&modulename=${moduleName}`;

      const response = await getData(requestUrl);

      console.log("all filter list response >>>", response);

      if (response.status && Array.isArray(response.data)) {
        // Format each filter for react-select and internal use
        return response.data.map((item, idx) => ({
          value: item.value || item.name || idx, // Use _id if available, fallback to name or index
          label: item.label || item.name || `Filter ${idx + 1}`,
          conditions: (() => {
            try {
              return typeof item.conditions === "string"
                ? JSON.parse(item.conditions)
                : item.conditions || [];
            } catch {
              return [];
            }
          })(),
          isDefault: item.isdefault || false,
          name: item.name,
        }));
      }
      return [];
    } catch (error) {
      console.error(error.message);
      return [];
    }
  };

  // Function to handle selecting a saved filter template
  const handleSelectFilterTemplate = (selectedTemplate) => {
    setSelectedFilterTemplate(selectedTemplate);
    if (selectedTemplate) {
      console.log("Selected condition", selectedTemplate.conditions);

      setFilters(selectedTemplate.conditions || []);
    } else {
      setFilters([]);
    }
  };

  // Function to save the current filter as a template
  const handleSaveFilterTemplate = async () => {
    try {
      // Set filter template data for saving
      let filterTemplateData = {
        name: filterTemplateName,
        conditions: JSON.stringify(filters), // Use conditions instead of parameters
        moduleslug: moduleSlug, // Example module slug
        isdefault: isDefaultTemplate,
      };

      console.log(`filterTemplateData ==========>>>`, filterTemplateData);

      let requestUrl = url.API_BASE_URL;

      // Check if updating or creating a new template
      if (selectedFilterTemplate) {
        requestUrl =
          requestUrl +
          url.API_UPDATE_FILTER +
          `/${selectedFilterTemplate.value}` +
          `?token=${token}`;
      } else {
        requestUrl = requestUrl + url.API_ADD_NEW_FILTER + `?token=${token}`;
      }

      console.log("selected filter template", selectedFilterTemplate);

      console.log("request url", requestUrl);

      const response = selectedFilterTemplate
        ? await putData(requestUrl, filterTemplateData)
        : await postData(requestUrl, filterTemplateData);

      console.log(`filterTemplateData response ==========>>>`, response);

      if (response.status) {
        setNotification("Filter template saved successfully!");
        loadSavedFilters();
        setTimeout(() => setNotification(""), 2500);
      }
    } catch (error) {
      console.error(error.message);
    }
  };

  // Function to delete the selected filter template
  const deleteSelectedFilter = async () => {
    if (selectedFilterTemplate) {
      const confirmDelete = window.confirm(
        "Are you sure you want to delete this template?"
      );
      if (!confirmDelete) return;

      try {
        // Mock API call to delete the filter template (replace with actual API call)
        const updatedFilters = savedFilters.filter(
          (filter) => filter.value !== selectedFilterTemplate.value
        );

        let requestUrl =
          url.API_BASE_URL +
          url.API_DELETE_FILTER +
          `/${selectedFilterTemplate.value}` +
          `?token=${token}`;

        await deleteData(requestUrl);

        setSavedFilters(updatedFilters);
        setSelectedFilterTemplate(null);
        setFilterTemplateName("");
        setIsDefaultTemplate(false);
        alert("Template deleted successfully!");
      } catch (error) {
        console.error("Error deleting template:", error);
      }
    } else {
      alert("No template selected to delete.");
    }
  };

  // Function to handle the search or apply filter action
  const searchHandler = () => {
    // console.log("called searchHandler");

    try {
      // Log the selected filter conditions
      // console.log("Selected Filter Conditions:", filters);

      // Set the filters data to the parent component via props
      setJsonFilterQuery([...filters]); // Create a new reference to the filters array

      // console.log("Filters sent to parent:", [...filters]);
    } catch (error) {
      console.error("Error applying filters:", error);
    }
  };

  // Function to toggle the Save Filter section
  const toggleSaveFilterSection = () => {
    setShowSaveFilterSection((prev) => !prev);
  };

  // Function to check if there are any conditions
  const hasConditions = () => filters.length > 0;

  // Function to load filters as saved for this module
  const loadSavedFilters = async () => {
    setIsLoadingSavedFilters(true);
    const filters = await fetchSavedFilters();

    // console.log("saved filters", filters);

    setSavedFilters(filters);
    setIsLoadingSavedFilters(false);
  };

  /* set filter fields to be available as options ---- */
  const filterFields = [
    {
      name: "title",
      label: "Title",
      type: "string",
    },
    {
      name: "promisepercent",
      label: "Promise Percent",
      type: "number",
    },
    {
      name: "user",
      label: "Owner",
      type: "multi-select",
      callback: getOwnersOptions,
    },
    {
      name: "followers",
      label: "Follower",
      type: "multi-select",
      callback: getFollowersOptions,
    },
    {
      name: "amount",
      label: "Price",
      type: "number",
    },
    {
      name: "tags",
      label: "Labels",
      type: "multi-select",
      callback: getAllTags, // get Current Loggedin user tags from API
    },
    {
      name: "createdAt",
      label: "Created At",
      type: "date",
    },
    {
      name: "updatedAt",
      label: "Updated At",
      type: "date",
    },
  ];

  // Dynamically add operators to filterFields based on type
  filterFields.forEach((field) => {
    field.operators = filterOperators[field.type] || [];
  });

  const fieldMeta = filterFields.find((f) => f.name === selectedField);

  const handleFieldChange = async (fieldName) => {
    setSelectedFieldValue(fieldName);
    // setSelectedField(field);
    setSelectedField(fieldName.value);
    setSelectedOperator("");
    setInputValue("");
    setInputValue2("");

    // const selectedFieldMeta = filterFields.find((f) => f.name === field);
    const selectedFieldMeta = filterFields.find(
      (f) => f.name === fieldName.value
    );

    console.log("selectedFieldMeta", selectedFieldMeta);

    if (selectedFieldMeta?.type === "multi-select") {
      // Reset options when switching to multi-select field
      setDynamicOptions([]);
      setInputQuery("");
    }
  };

  const handleAddFilter = () => {
    console.log("inputValue", inputValue);
    if (!selectedField || !selectedOperator) return;

    // Handle different field types
    const fieldMeta = filterFields.find((f) => f.name === selectedField);
    let value = inputValue;

    // Handle multi-select fields (array of objects)
    if (fieldMeta?.type === "multi-select") {
      // Extract just the values for storage
      // value = Array.isArray(inputValue)
      //   ? inputValue.map((item) => item.value)
      //   : [];
    } else if (fieldMeta?.type === "boolean") {
      value = selectedOperator === "true";
    } else if (selectedOperator === "between" && fieldMeta?.type === "date") {
      if (!inputValue || !inputValue2) return; // Ensure both dates are provided
      value = [inputValue, inputValue2];
    } else if (selectedOperator === "between" && fieldMeta?.type === "number") {
      if (!inputValue || !inputValue2) return; // Ensure both numbers are provided
      value = [parseFloat(inputValue), parseFloat(inputValue2)];
    } else if (fieldMeta?.type === "number") {
      if (!inputValue) return; // Ensure input value is provided
      value = parseFloat(inputValue); // Convert to number
    } else if (!inputValue) {
      return; // Ensure input value is provided for other types
    }

    const newFilter = {
      field: selectedField,
      operator: selectedOperator,
      value: value,
    };

    if (editIndex !== null) {
      const updatedFilters = [...filters];
      updatedFilters[editIndex] = newFilter;
      setFilters(updatedFilters);
      setEditIndex(null);
    } else {
      setFilters([...filters, newFilter]);
    }

    // Reset fields after adding the filter
    setSelectedFieldValue(null);
    setSelectedField("");
    setSelectedOperator("");
    setInputValue("");
    setInputValue2("");
  };

  const handleRemoveFilter = (index) => {
    const updatedFilters = filters.filter((_, i) => i !== index);
    setFilters(updatedFilters);
  };

  const handleEditFilter = (index) => {
    // console.log("edit called", index);
    const filterToEdit = filters[index];
    const fieldMetaToEdit = filterFields.find(
      (f) => f.name === filterToEdit.field
    );
    setSelectedFieldValue({
      label: fieldMetaToEdit.label,
      value: fieldMetaToEdit.name,
    });
    setSelectedField(filterToEdit.field);
    setSelectedOperator(filterToEdit.operator);

    if (filterToEdit.operator === "between") {
      setInputValue(filterToEdit.value[0]);
      setInputValue2(filterToEdit.value[1] ?? "");
    } else if (fieldMetaToEdit?.type === "multi-select") {
      // For multi-selects, the value is an array of {label, value} objects
      setInputValue(filterToEdit.value);
      setInputValue2("");
    } else {
      // For other single value types (string, number, date, boolean)
      setInputValue(filterToEdit.value);
      setInputValue2("");
    }
    setEditIndex(index);
  };

  const resetHandler = () => {
    setSelectedFieldValue(null);
    setSelectedField("");
    setSelectedOperator("");
    setInputValue("");
    setInputValue2("");
    setFilters([]);
    setEditIndex(null);
    setSelectedFilterTemplate(null);
    setFilterTemplateName("");
    setIsDefaultTemplate(false);
    setInputQuery(""); // Reset search query
    setDebouncedQuery(""); // Reset debounced query
    setDynamicOptions([]); // Reset dynamic options
  };

  // Fetch options when debounced query changes
  useEffect(() => {
    if (selectedField && fieldMeta?.callback && debouncedQuery.length >= 2) {
      const fetchOptions = async () => {
        const options = await fieldMeta.callback(debouncedQuery);
        setDynamicOptions(options);
      };
      fetchOptions();
    } else {
      setDynamicOptions([]);
    }
  }, [debouncedQuery, selectedField]);

  // Add debounce effect for search input
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(inputQuery);
    }, 100);

    return () => {
      clearTimeout(handler);
    };
  }, [inputQuery]);

  // Fetch saved filters on component mount
  useEffect(() => {
    loadSavedFilters();
  }, []);

  useEffect(() => {
    if (isFilterReset) {
      resetHandler();
      setIsFilterReset(false);
    }
  }, [isFilterReset]);

  return (
    <div
      className="offcanvas offcanvas-end custom-offcanvas" // Add a custom class for styling
      tabIndex="-1"
      id="opportunityAdvFilter"
      aria-labelledby="opportunityAdvFilterLabel"
      //data-bs-backdrop="false"
    >
      <div className="offcanvas-header bg-primary text-white">
        <h5 className="offcanvas-title">Filter</h5>
        <button
          type="button"
          className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
          onClick={resetHandler}
        ></button>
      </div>
      <div className="offcanvas-body">
        <form onSubmit={(e) => e.preventDefault()}>
          {/* Top Section: Select Saved Filter */}
          <div className="mb-4 border-bottom pb-3">
            <div className="d-flex align-items-center gap-2">
              <h6 className="fw-bold mb-3 flex-shrink-0">
                Select Saved Filter
              </h6>
            </div>
            <Select
              isClearable
              options={savedFilters}
              value={selectedFilterTemplate}
              onChange={(selectedTemplate) => {
                handleSelectFilterTemplate(selectedTemplate);
                setFilterTemplateName(selectedTemplate?.label || "");
                setIsDefaultTemplate(selectedTemplate?.isDefault || false);
              }}
              placeholder="Select a saved filter template"
              // className="border border-secondary rounded"
              styles={filterAdvanceSelectStyle()}
            />

            {isLoadingSavedFilters && (
              <span
                className="spinner-border spinner-border-sm text-primary ms-2"
                role="status"
                aria-hidden="true"
              ></span>
            )}
          </div>

          {/* Middle Section: Create Filter */}
          <div className="mb-4 border-bottom pb-3">
            <h6 className="fw-bold mb-3 text-primary">Create Filter</h6>
            <div className="mb-3">
              <label className="fw-semibold">Select Field</label>
              {/* <select
                className="form-select border border-secondary rounded px-2"
                value={selectedField}
                onChange={(e) => handleFieldChange(e.target.value)}
              >
                <option value="">Select Field</option>
                {filterFields.map((field) => (
                  <option key={field.name} value={field.name}>
                    {field.label}
                  </option>
                ))}
              </select> */}
              <Select
                styles={filterAdvanceSelectStyle()}
                placeholder={t("Select Field")}
                options={filterFields.map((field) => ({
                  ...field,
                  value: field.name,
                }))}
                value={selectedFieldValue}
                onChange={(val) => handleFieldChange(val)}
              />
            </div>

            {fieldMeta && (
              <div className="mb-3">
                <label className="fw-semibold">Operator</label>
                <select
                  className="form-select border border-secondary rounded px-2"
                  value={selectedOperator}
                  onChange={(e) => setSelectedOperator(e.target.value)}
                >
                  <option value="">Select Operator</option>
                  {fieldMeta.operators.map((op) => (
                    <option key={op} value={op}>
                      {op}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {selectedOperator && fieldMeta?.type === "string" && (
              <div className="mb-3">
                <label className="fw-semibold">Value</label>
                <input
                  type="text"
                  className="form-control border border-secondary rounded px-2"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              </div>
            )}

            {selectedOperator && fieldMeta?.type === "date" && (
              <>
                <div className="mb-3">
                  <label className="fw-semibold">
                    {selectedOperator === "between" ? "From" : "Date"}
                  </label>
                  <input
                    type="date"
                    className="form-control border border-secondary rounded px-2"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                </div>
                {selectedOperator === "between" && (
                  <div className="mb-3">
                    <label className="fw-semibold">To</label>
                    <input
                      type="date"
                      className="form-control border border-secondary rounded px-2"
                      value={inputValue2}
                      onChange={(e) => setInputValue2(e.target.value)}
                    />
                  </div>
                )}
              </>
            )}

            {selectedOperator && fieldMeta?.type === "number" && (
              <>
                <div className="mb-3">
                  <label className="fw-semibold">
                    {selectedOperator === "between" ? "From" : "Value"}
                  </label>
                  <input
                    type="number"
                    className="form-control border border-secondary rounded px-2"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                </div>
                {selectedOperator === "between" && (
                  <div className="mb-3">
                    <label className="fw-semibold">To</label>
                    <input
                      type="number"
                      className="form-control border border-secondary rounded px-2"
                      value={inputValue2}
                      onChange={(e) => setInputValue2(e.target.value)}
                    />
                  </div>
                )}
              </>
            )}

            {selectedOperator && fieldMeta?.type === "multi-select" && (
              <div className="mb-3">
                <label className="fw-semibold">Choose Options</label>
                <Select
                  isMulti
                  options={dynamicOptions}
                  value={inputValue}
                  onInputChange={(newValue) => setInputQuery(newValue)}
                  onChange={(selectedOptions) => {
                    setInputValue(selectedOptions || []);
                  }}
                  // className="basic-multi-select border border-secondary rounded"
                  styles={filterAdvanceSelectStyle()}
                  classNamePrefix="select"
                  placeholder="Type at least 2 characters to search..."
                  noOptionsMessage={({ inputValue }) =>
                    inputValue.length < 2
                      ? t("Type 2+ characters to search")
                      : t("No options found")
                  }
                />
              </div>
            )}

            <div className="d-flex gap-2">
              <button
                type="button"
                className="btn btn-outline-primary"
                onClick={handleAddFilter}
              >
                {editIndex !== null ? "Update in Filter" : "Add in Filter"}
              </button>
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={() => {
                  setSelectedFieldValue(null);
                  setSelectedField("");
                  setSelectedOperator("");
                  setInputValue("");
                  setInputValue2("");
                  setEditIndex(null);
                }}
              >
                Clear
              </button>
            </div>
          </div>

          {/* Display Filter JSON Data ********* REMOVE THIS IN PROD
           ********************************************************* */}

          {/* Display Added Filters */}
          {hasConditions() && (
            <div
              className="mt-4 border-bottom pb-3"
              style={{
                background: "#f8fafc",
                borderRadius: "12px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
                padding: "20px 16px",
                marginBottom: "24px",
                border: "1px solid #e3e8ef",
              }}
            >
              <h6
                className="fw-bold mb-3"
                style={{
                  color: "#1976d2",
                  letterSpacing: "0.5px",
                  fontSize: "1.1rem",
                }}
              >
                Added Filters
              </h6>
              {filters.map((filter, index) => (
                <div
                  key={index}
                  className="card mb-2"
                  style={{
                    border: "none",
                    borderRadius: "8px",
                    background: "#fff",
                    boxShadow: "0 1px 4px rgba(0,0,0,0.03)",
                  }}
                >
                  <div
                    className="card-body d-flex justify-content-between align-items-center"
                    style={{
                      padding: "12px 16px",
                    }}
                  >
                    <div className="flex-grow-1" style={{ fontSize: "1rem" }}>
                      <span
                        className="fw-semibold"
                        style={{ color: "#374151" }}
                      >
                        {
                          filterFields.find((f) => f.name === filter.field)
                            ?.label
                        }
                      </span>{" "}
                      {filter.operator === "between" &&
                      Array.isArray(filter.value) ? (
                        <>
                          <span
                            style={{
                              background: "#e3e8ef",
                              borderRadius: "4px",
                              padding: "2px 8px",
                              fontWeight: 500,
                              color: "#1976d2",
                            }}
                          >
                            between
                          </span>{" "}
                          <span
                            className="fw-semibold"
                            style={{ color: "#374151" }}
                          >
                            {
                              // Format as date if field type is date, else show raw value
                              (() => {
                                const fieldMeta = filterFields.find(
                                  (f) => f.name === filter.field
                                );
                                if (fieldMeta?.type === "date") {
                                  return filter.value[0]
                                    ? new Date(
                                        filter.value[0]
                                      ).toLocaleDateString()
                                    : "";
                                }
                                return filter.value[0];
                              })()
                            }
                          </span>{" "}
                          <span
                            style={{
                              background: "#e3e8ef",
                              borderRadius: "4px",
                              padding: "2px 8px",
                              fontWeight: 500,
                              color: "#1976d2",
                            }}
                          >
                            and
                          </span>{" "}
                          <span
                            className="fw-semibold"
                            style={{ color: "#374151" }}
                          >
                            {(() => {
                              const fieldMeta = filterFields.find(
                                (f) => f.name === filter.field
                              );
                              if (fieldMeta?.type === "date") {
                                return filter.value[1]
                                  ? new Date(
                                      filter.value[1]
                                    ).toLocaleDateString()
                                  : "";
                              }
                              return filter.value[1];
                            })()}
                          </span>
                        </>
                      ) : filterFields.find((f) => f.name === filter.field)
                          ?.type === "boolean" ? (
                        <>
                          <span
                            style={{
                              background: "#e3e8ef",
                              borderRadius: "4px",
                              padding: "2px 8px",
                              fontWeight: 500,
                              color: "#1976d2",
                            }}
                          >
                            :
                          </span>{" "}
                          <span
                            className="fw-semibold"
                            style={{ color: "#374151" }}
                          >
                            {filter.value === "true" || filter.value === true
                              ? "True"
                              : "False"}
                          </span>
                        </>
                      ) : (
                        <>
                          <span
                            style={{
                              background: "#e3e8ef",
                              borderRadius: "4px",
                              padding: "2px 8px",
                              fontWeight: 500,
                              color: "#1976d2",
                            }}
                          >
                            {filter.operator}
                          </span>{" "}
                          <span
                            className="fw-semibold"
                            style={{ color: "#374151" }}
                          >
                            {Array.isArray(filter.value)
                              ? filter.value.map((v) => v.label || v).join(", ")
                              : filter.value}
                          </span>
                        </>
                      )}
                    </div>
                    <div className="d-flex gap-1">
                      <button
                        className="btn btn-outline-primary btn-sm p-1"
                        onClick={() => handleEditFilter(index)}
                      >
                        <i
                          className="material-symbols-outlined"
                          style={{ fontSize: "16px" }}
                        >
                          edit
                        </i>
                      </button>
                      <button
                        className="btn btn-outline-danger btn-sm p-1"
                        onClick={() => handleRemoveFilter(index)}
                      >
                        <i
                          className="material-symbols-outlined"
                          style={{ fontSize: "16px" }}
                        >
                          close
                        </i>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Bottom Section: Save Filter Template */}
          {hasConditions() && (
            <div className="mt-4">
              <button
                type="button"
                className="btn btn-link text-primary p-0"
                onClick={toggleSaveFilterSection}
              >
                {selectedFilterTemplate
                  ? "Update Template"
                  : "Save as Template"}
              </button>
              {showSaveFilterSection && (
                <div className="mt-3">
                  <label className="fw-semibold">Filter Template Name</label>
                  <input
                    type="text"
                    className="form-control border border-secondary rounded mb-2"
                    placeholder="Enter filter template name"
                    value={filterTemplateName}
                    onChange={(e) => setFilterTemplateName(e.target.value)}
                  />
                  <div className="form-check mb-3">
                    <input
                      className="form-check-input border border-secondary rounded"
                      type="checkbox"
                      id="defaultTemplate"
                      checked={isDefaultTemplate}
                      onChange={(e) => setIsDefaultTemplate(e.target.checked)}
                    />
                    <label
                      className="form-check-label fw-semibold ms-2"
                      htmlFor="defaultTemplate"
                    >
                      Save as Default Template
                    </label>
                  </div>
                  <div className="d-flex gap-2">
                    <button
                      type="button"
                      className="btn btn-outline-primary"
                      onClick={handleSaveFilterTemplate}
                    >
                      {selectedFilterTemplate
                        ? "Update Template"
                        : "Save Template"}
                    </button>
                    {selectedFilterTemplate && (
                      <button
                        type="button"
                        className="btn btn-outline-danger"
                        onClick={deleteSelectedFilter}
                      >
                        Delete Template
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </form>
      </div>

      {notification && (
        <span className="ms-2 text-success small">{notification}</span>
      )}

      {/* Fixed Apply Filter and Reset Buttons */}
      <div className="offcanvas-footer bottom-0 start-0 end-0 bg-white p-3 border-top d-flex justify-content-between align-items-center">
        <button
          type="button"
          className={`btn ${
            hasConditions() ? "btn-primary" : "btn-outline-primary"
          } flex-grow-1`}
          onClick={hasConditions() ? searchHandler : null}
          disabled={!hasConditions()}
        >
          Apply Filter
        </button>
        <button
          type="button"
          className="btn btn-outline-secondary ms-2"
          onClick={() => {
            resetHandler();
            reloadList();
          }}
        >
          Reset
        </button>
      </div>
    </div>
  );
};

export default OpportunityAdvFilterPopup;
