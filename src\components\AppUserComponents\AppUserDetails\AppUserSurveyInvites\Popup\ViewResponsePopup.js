/* eslint-disable */
import React, { useContext, useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { useTranslation } from "react-i18next";

const ViewResponsePopup = ({ viewInviteId, setViewInviteId }) => {
  const starCount = [1, 2, 3, 4, 5];
  const { t } = useTranslation(); //for translation
  const [questionList, setquestionList] = useState([]);
  const [surveyResponsed, setSurveyResponsed] = useState(0);

  //get invite details
  const getSurveyInviteDetails = async () => {
    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_SURVEY_INVITE_DETAILS +
        `/${viewInviteId}`;

      console.log(requestURL);

      const response = await getData(requestURL);

      console.log(response);

      if (response.status) {
        setquestionList(response.data.questionlist);
        setSurveyResponsed(response.data.surveyresponses.length);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (viewInviteId) {
      getSurveyInviteDetails();
    }
  }, [viewInviteId]);

  return (
    <div
      className="offcanvas md offcanvas-end bg-white border-0"
      tabIndex="-1"
      id="offcanvasViewResponse"
      aria-labelledby="offcanvasViewResponseLabel"
    >
      <div className="offcanvas-header p-4 pb-0">
        <h3 className="offcanvas-title" id="addToChallengeLabel">
          {t("Response")}
        </h3>
        <button
          type="button"
          className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
          onClick={() => {
            setquestionList([]);
            setSurveyResponsed(0);
            setViewInviteId(null);
          }}
        ></button>
      </div>
      <div className="offcanvas-body p-4">
        <form
          onSubmit={(e) => e.preventDefault()}
          className={`d-flex flex-column ${
            surveyResponsed === 0 ? "h-25" : "h-100"
          }`}
        >
          {surveyResponsed === 0 ? (
            <div className="form-group mb-4 mb-md-5">
              {" "}
              <label htmlFor="" className="d-block fs-lg fw-semibold mb-2">
                Awaiting for response ....
              </label>
            </div>
          ) : null}

          {questionList.map((question, index) => {
            return (
              <div className="form-group mb-4 mb-md-5" key={index}>
                {question.surveyanswer == "" ? null : (
                  <label htmlFor="" className="d-block fs-lg fw-semibold mb-2">
                    {question.name}
                  </label>
                )}

                {question.surveyanswer ==
                "" ? null : question.questiontypeslug === "RATING" ? (
                  <ul className="d-flex">
                    {starCount.map((count, index) => {
                      return (
                        <li key={index}>
                          <span
                            className={`d-block material-symbols-outlined ${
                              index < parseInt(question.surveyanswer)
                                ? "text-primary icon-fill"
                                : "text-gray"
                            }  `}
                          >
                            star
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                ) : question.questiontypeslug === "SINGLE SELECT" ? (
                  <ul className="d-flex flex-wrap gap-2 fs-sm fw-semibold">
                    <li className="px-3 py-2 border border-gray-300 rounded-10">
                      {question.surveyanswer}
                    </li>
                  </ul>
                ) : question.questiontypeslug === "MULTI SELECT" ? (
                  <ul className="d-flex flex-wrap gap-2 fs-sm fw-semibold">
                    {question.surveyanswer.split(", ").map((answer, index2) => {
                      return (
                        <li
                          className="px-3 py-2 border border-gray-300 rounded-10"
                          key={index2}
                        >
                          {answer}
                        </li>
                      );
                    })}
                  </ul>
                ) : question.questiontypeslug === "TEXT" ||
                  question.questiontypeslug === "COMMENTS" ? (
                  <p className="fs-md">{question.surveyanswer}</p>
                ) : null}
              </div>
            );
          })}
        </form>

        <Link
          to={`/admin/survey/invite/${viewInviteId}`}
          target="_blank"
          className="btn btn-primary"
        >
          Go to Survey
        </Link>
      </div>
    </div>
  );
};

export default ViewResponsePopup;
