//function common header crm links
// Helper function to check user permissions
const hasPermission = (currentUserRole, moduleaccess, moduleName) => {
  return (
    currentUserRole === "ADMIN" ||
    currentUserRole === "SUPER_ADMIN" ||
    moduleaccess.includes(moduleName)
  );
};

const crmCommonHeaderLinkLists = (t) => {
  // loggedin user details to get the role
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  // get the role and allow for admin or superadmin
  const currentUserRole = userInfo.role ? userInfo.role.slug : "USER";

  // get module access for roles other than admin and superadmin
  const moduleaccess = localStorage.getItem("moduleaccess");

  //console.log('currentUserRole', currentUserRole);

  // console.log("moduleaccess", moduleaccess);

  const headerTabs = [];

  const tabsConfig = [
    { module: "MOD_LEAD", title: t("Leads"), link: "/admin/crm/lead/list" },
    {
      module: "MOD_ECOLEADS",
      title: t("EcoLeads"),
      link: "/admin/crm/ecolead/list",
    },
    {
      module: "MOD_CONTACT",
      title: t("Contact"),
      link: "/admin/crm/contact/list",
    },
    {
      module: "MOD_CONVERSATION",
      title: t("Conversation"),
      link: "/admin/crm/conversation/recieved",
    },
    { module: "MOD_LIST", title: t("List"), link: "/admin/crm/list" },
    {
      module: "MOD_MAIL_TEMPLATE",
      title: t("Mail Templates"),
      link: "/admin/crm/mailtemplate/list",
    },
    { module: "MOD_LABEL", title: t("Label"), link: "/admin/crm/label/list" },
    {
      module: "MOD_PROCESS",
      title: t("Activity Process"),
      link: "/admin/crm/process/list",
    },
    {
      module: "MOD_OPPORTUNITY",
      title: t("Opportunities"),
      link: "/admin/crm/opportunity/list",
    },
  ];

  tabsConfig.forEach((tabConfig) => {
    if (hasPermission(currentUserRole, moduleaccess, tabConfig.module)) {
      headerTabs.push({ title: tabConfig.title, link: tabConfig.link });
    }
  });

  const noteTab = {
    title: t("Notes"),
    link: "/admin/crm/note/list",
  };

  headerTabs.push(noteTab);

  return headerTabs;
};

export { crmCommonHeaderLinkLists };
