/* eslint-disable */
import { Grid } from "@mui/material";

import AccessDeniedView from "components/Common/AccessDeniedView/AccessDeniedView";
import AdminCard from "./Card/AdminCard";

const AdministrationBody = () => {
  // Configuration for administration cards. Add more card groups here
  const adminCardList = [
    {
      roles: ["SUPER_ADMIN"],
      cards: [
        {
          title: "Ecosystem",
          description: "Show all Ecosystems associated with the app",
          link: "/admin/administration/ecosystems",
          linkText: "Manage Ecosystems",
        },
        {
          title: "App Users Role",
          description: "Show various roles of App Users",
          link: "/admin/administration/roles",
          linkText: "Manage Roles",
        },
        {
          title: "Action Logs",
          description: "Show various logs of this application",
          link: "/admin/administration/actionlogs",
          linkText: "View Action logs",
        },
      ],
    },
    {
      roles: ["SUPER_ADMIN"],
      cards: [
        {
          title: "Modules",
          description: "Show Ecosystem Modules",
          link: "/admin/administration/modules",
          linkText: "Manage Modules",
        },
        {
          title: "Module Privileges By User Role",
          description: "Module privileges for to app user by roles",
          link: "/admin/administration/moduleroleprivilege",
          linkText: "Manage Module Privileges",
        },
        {
          title: "Gpt Prompt",
          description: "Show gpt list of this application",
          link: "/admin/administration/gptprompt",
          linkText: "View gpt prompts",
        },
      ],
    },
    {
      roles: ["ADMIN", "SUPER_ADMIN"],
      cards: [
        {
          title: "Module Roles",
          description: "Add roles in different modules for access rights",
          link: "/admin/administration/moduleroles",
          linkText: "Manage Module Roles",
        },
        {
          title: "Categories",
          description: "Show all Categories added in this ecosystems",
          link: "/admin/administration/categories/list",
          linkText: "Manage Categories",
        },
        {
          title: "Plans",
          description: "Plans for subscription in current Ecosystem",
          link: "/admin/administration/plans",
          linkText: "Manage Plans",
        },
      ],
    },
    {
      roles: ["ADMIN", "SUPER_ADMIN"],
      cards: [
        {
          title: "Tags",
          description: "Manage tags for all users",
          link: "/admin/administration/tags",
          linkText: "Manage Tags",
        },
        {
          title: "Activity Process Template",
          description: "Manage kanban template for organize labels",
          link: "/admin/administration/activityprocess/list",
          linkText: "Manage Activity Process Template",
        },
        {
          title: "Mail Template",
          description: "Manage mail template for organize templates",
          link: "/admin/administration/mailtemplate/list",
          linkText: "Manage Mail Template",
        },
      ],
    },
  ];

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const currentUserRole = userInfo.role ? userInfo.role.slug : "";

  // Check admin access
  if (!["ADMIN", "SUPER_ADMIN"].includes(currentUserRole)) {
    return <AccessDeniedView />;
  }

  // Render card groups
  const renderCardGroups = () =>
    adminCardList.map((group, index) => {
      if (group.roles.includes(currentUserRole)) {
        return (
          <Grid container spacing={1} margin={1} key={`group-${index}`}>
            {group.cards.map((card, index) => (
              <AdminCard key={index} card={card} />
            ))}
          </Grid>
        );
      }
      return null;
    });

  // Main content for admins
  return (
    <div id="content_wrapper">
      <section className="dashboard-wrapper">{renderCardGroups()}</section>
    </div>
  );
};

export default AdministrationBody;
