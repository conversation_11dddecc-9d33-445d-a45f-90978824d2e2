/* eslint-disable */
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import CrmLeadInformationCommonHeader from "../../common/CrmLeadInformationCommonHeader";
import CrmLeadInformationLeftPanel from "../LefPanel/CrmLeadInformationLeftPanel";

import { assetImages } from "constants";

import AlertNotification from "components/Common/AlertNotification/AlertNotification";

import NoteSection from "components/Common/NoteComponent/NoteSection";

const CrmLeadInformationBody = () => {
  const params = useParams();

  const moduleAccess = localStorage.getItem("moduleaccess");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const token = localStorage.getItem("token");

  const { t } = useTranslation(); //for translation

  const [leadName, setleadName] = useState("");
  const [leadImage, setleadImage] = useState("");
  const [isAuthenticatedUser, setisAuthenticatedUser] = useState(false);

  const [noteAccessUserList, setnoteAccessUserList] = useState([]);

  const [leadHistory, setLeadHistory] = useState([]);

  //alert requirements
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [messageType, setMessageType] = useState("");

  //function for get lead history of modified lead
  const getLeadHistory = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_LEAD_HISTORY +
        `?token=${token}&leadid=${params.id}`;

      const response = await getData(requestUrl);

      console.log("response in lead history------>", response);

      if (response.status) {
        setLeadHistory(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const onAlertClose = () => {
    setShowAlert(false);
    setAlertMessage("");
    setMessageType("");
  };

  useEffect(() => {
    if (params.id) {
      getLeadHistory();
    }
  }, [params.id]);

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    (moduleAccess.includes("MOD_CRM") && moduleAccess.includes("MOD_LEAD"))
  ) {
    return (
      <div id="content_wrapper">
        <section className="crm-wrapper bg-white">
          {/* ------ lead information common header ------ */}
          <CrmLeadInformationCommonHeader
            moduleName="leadinformation"
            leadName={leadName}
            leadImage={leadImage}
            isAuthenticatedUser={isAuthenticatedUser}
          />
          {/* ------ lead information section start ------ */}
          <div className="crm-info-container py-4 py-md-5">
            <div className="container-fluid px-lg-5">
              <div className="row">
                {/* ------- lead information left panel start ------ */}
                <div className="col-lg-8 mb-4 mb-lg-0">
                  <CrmLeadInformationLeftPanel
                    setleadName={setleadName}
                    setleadImage={setleadImage}
                    setisValidUser={setisAuthenticatedUser}
                    setShowAlert={setShowAlert}
                    setAlertMessage={setAlertMessage}
                    setMessageType={setMessageType}
                    setnoteAccessUserList={setnoteAccessUserList}
                  />
                </div>
                {/* ------- lead information left panel end ------ */}

                {/* ------- lead information right panel start ------ */}
                <div className="col-lg-4">
                  <NoteSection
                    noteAccessUserList={noteAccessUserList}
                    leadId={params.id}
                    setShowAlert={setShowAlert}
                    setAlertMessage={setAlertMessage}
                    setMessageType={setMessageType}
                  />

                  <div className="p-4 border border-gray-300 rounded-10 shadow-sm mt-3">
                    <h3>{t("History")}</h3>

                    <div className="note-list mb-1">
                      {leadHistory?.length > 0 ? (
                        <ul className="mt-3 d-flex flex-column gap-2 list-group">
                          {leadHistory.map((item, index) => (
                            <li
                              key={item._id}
                              className={
                                item?.updatedbyname && item.changedfieldnames
                                  ? "list-group-item rounded-5"
                                  : "d-none"
                              }
                            >
                              <div>
                                <strong>Date :</strong> {item.dateofchange}
                              </div>
                              <div>
                                <strong>Updated By :</strong>{" "}
                                {item?.updatedbyname || "N/A"}
                              </div>
                              <div>
                                <strong>Updated Fields :</strong>{" "}
                                {item.changedfieldnames || "N/A"}
                              </div>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-muted text-center mt-3">
                          No history found.
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                {/* ------- lead information right panel end ------ */}
              </div>
            </div>
          </div>
          {/* ------ lead information section end ------ */}

          {showAlert && (
            <AlertNotification
              showAlert={showAlert}
              message={alertMessage}
              alertType={messageType}
              onClose={onAlertClose}
            />
          )}
        </section>
      </div>
    );
  } else {
    return (
      <div className="empty_access text-center">
        <div className="empty_pic mb-4">
          {" "}
          <img src={assetImages.emptyVector} alt="" />
        </div>
        <div className="empty_text">
          <p className="fs-lg text-gray fw-semibold mb-4">
            {t("Sorry....! You don't have privilege to see this content")}
          </p>
        </div>
      </div>
    );
  }
};

export default CrmLeadInformationBody;
