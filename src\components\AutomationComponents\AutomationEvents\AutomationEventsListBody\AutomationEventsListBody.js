import React, { useEffect, useState, useMemo } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { MaterialReactTable } from "material-react-table";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import AutomationEventsHeader from "../AutomationEventsHeader/AutomationEventsHeader";
import TabsHeader from "components/Common/TabsHeader/TabsHeader";
import {automationHeaderLinks} from "helper/AutomationHelper/AutomationMenuLinks";

import typeOptions from "data/Automation/Type.json";
import actionOptions from "data/Automation/Actions.json";

const AutomationEventsListBody = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [eventsList, setEventsList] = useState([]);

  const [reloadData, setReloadData] = useState(false);

  const commonHeaderObject = automationHeaderLinks(t);

  // Fetch automation events in process
  const fetchAutomationEvents = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("token");
      const requestUrl = `${url.API_BASE_URL}${url.API_AUTOMATION_EVENTS}?token=${token}`;

      console.log("Fetching Automation Events from URL:", requestUrl); // Debug log
      const response = await getData(requestUrl);

      console.log("Automation Events Response:", response);

      if (response.status) {
        setEventsList(response.data);
      } else {
        console.error("Error fetching automation events:", response.message);
      }
    } catch (error) {
      console.error("Error fetching automation events:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAutomationEvents();
  }, []);

  // Define table columns
  const columns = useMemo(
    () => [
      {
        accessorKey: '#',
        header: t('Actions'),
        Cell: ({ row }) => (
          <Link
            className="action_btn_mui"
            to="#"
          >
            <span className="d-block material-symbols-outlined horz_icon">
              more_horiz
            </span>
          </Link>
        ),
        enableColumnActions: false, // Hides the column action icon
        enableColumnDragging: false, // Hides the move icon
        enableSorting: false,
      },
      {
        accessorKey: "automation.trigger",
        header: t("Condition"),
        Cell: ({ row }) => {
          const module = row.original.automation?.trigger?.module?.name?? t("N/A");
          const triggerType = row.original.automation?.trigger?.type?? t("N/A");
          const tag = row.original.automation?.trigger?.tag?.title?? t("N/A");
          const matchedType = typeOptions.find(option => option.value === triggerType);
            return (
            <div>
              <div><strong>{t("Module")}:</strong> {module}</div>
              <div><strong>{t("Type")}:</strong> {matchedType?matchedType.label:triggerType}</div>
              <div><strong>{t("Tag")}:</strong> {tag}</div>
            </div>
            );
        }
      },
      {
        accessorKey: "componentid",
        header: t("Component"),
        Cell: ({ row }) => {
          if(row.original.leadid) {
            return (
                  <div>
                    <div>{row.original.lead.name}</div>
                    <div className="text-muted">{row.original.lead.email}</div>
                  </div>);
          }

          if (row.original.userid) {
            return (
              <div>
                <div>{row.original.user.name}</div>
                <div className="text-muted">{row.original.user.email}</div>
              </div>
            );
          }

          if(row.original.contactid) {
            let contactName = '';
            let contactEmail = '';
            if(row.original.contactowner && row.original.contactowner.id != row.original.automation.owner) {
              contactName = row.original.contactowner?.name;
              contactEmail = row.original.contactowner?.email;
            }else{
              contactName = row.original.contactmember?.name;
              contactEmail = row.original.contactmember?.email;
            }
            return (
              <div>
                <div>{contactName}</div>
                <div className="text-muted">{contactEmail}</div>
              </div>
            );
          }



        },
      },
      {
        accessorKey: "automation.steps",
        header: t("Automation Steps"),
        Cell: ({ row }) => {
          const steps = row.original.automation?.steps || [];
          return (
            <div>
              {steps.map((step, index) => {
                const matchedAction = actionOptions.find(option => option.value === step.action);
                return (
                  <div key={index}>
                    <strong>{t("Step")} {step.order}:</strong> {matchedAction ? matchedAction.label : step.action}
                  </div>
                );
              })}
            </div>
          );
        }
      },
      {
        accessorKey: "execution.stepscompleted",
        header: t("Steps Executed"),
        Cell: ({ row }) => {
          const stepscompleted = row.original.execution?.stepscompleted || [];
          return (
            <div>
              {stepscompleted.map((step, index) => {
                const matchedAction = actionOptions.find(option => option.value === step.action);
                return (
                  <div key={index}>
                  <strong>{t("Step")} {step.order}:</strong> {matchedAction ? matchedAction.label : step.action}
                  <div className="text-muted">
                    {new Date(step.createdAt).toLocaleDateString() || t("N/A")} {new Date(step.createdAt).toLocaleTimeString() || t("N/A")}
                  </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
      {
        accessorKey: "execution.stepsfailed",
        header: t("Steps Failed"),
        Cell: ({ row }) => {
          const stepsfailed = row.original.execution?.stepsfailed || [];
          return (
            <div>
              {stepsfailed.map((step, index) => {
                const matchedAction = actionOptions.find(option => option.value === step.action);
                return (
                  <div key={index}>
                    <strong>{t("Step")} {step.order}:</strong> {matchedAction ? matchedAction.label : step.action}
                  </div>
                );
              })}
            </div>
          );
        }
      },
      
      {
        accessorKey: "execution.executionstatus",
        header: t("Status"),
        Cell: ({ row }) => (
          <div>
            <span> {row.original.execution.executionstatus || t("N/A")}</span>
            <div className="text-muted">
              {row.original.execution.executionstatus != "completed" ? t("Current Step") : t("Last Step")}:
              {row.original.execution?.currentsteporder??' N/A'}
            </div>
          </div>
          
        ),
      },
      {
        accessorKey: "createdAt",
        header: t("Date"),
        Cell: ({ row }) => (
          <div>
            <span>{new Date(row.original.createdAt).toLocaleDateString() || t("N/A")}</span>
            <div className="text-muted"> {new Date(row.original.createdAt).toLocaleTimeString() || t("N/A")}</div>
          </div>
        ),
      },

    ],
    [t]
  );

  const refreshRecords = () => {
    // Logic to refresh the records
    setReloadData(true);
  };

  useEffect(() => {
    if (reloadData) {
      fetchAutomationEvents();
      setReloadData(false);
    }
  }, [reloadData]);

  return (
    <div id="content_wrapper">
      <section className="events-wrapper bg-white pb-5">
        <TabsHeader
              commonHeaderObject={commonHeaderObject}
              activeOption={t("Automation Events")}
            />

        <div className="container-fluid px-lg-5">

          <AutomationEventsHeader reloadList={refreshRecords} />

          {isLoading ? (
            <div className="placeholder-glow d-flex flex-column gap-4">
              <span className="placeholder placeholder-lg bg-secondary col-12"></span>
              <span className="placeholder placeholder-lg bg-secondary col-8"></span>
              <span className="placeholder placeholder-lg bg-secondary col-4"></span>
            </div>
          ) : (
            <div className="table-wrapper">
              <MaterialReactTable
                columns={columns}
                data={eventsList}
                muiTableContainerProps={{
                  sx: {
                    maxHeight: "60vh",
                  },
                }}
                enableStickyHeader
              />
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default AutomationEventsListBody;
