/* eslint-disable */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Select from "react-select";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData, postData } from "utils/Gateway";

import { getAllCategories } from "helper/CategoryHelper/CategoryHelper";
import { ecosystemSlug } from "Config/Config";

const AddEditSkillPointModal = ({
  skillPointIndex,
  setSkillPointIndex,
  skillPointData,
  setSkillPointData,
  skillPointListBlock,
  setSkillPointListBlock,
}) => {
  const { t } = useTranslation(); //for translation
  const token = localStorage.getItem("token");

  const [allSkills, setAllSkills] = useState([]);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!skillPointData.skill && !skillPointData.skillname.trim()) {
      newErrors.skill = t("Skill or Skill Name is required");
      newErrors.skillname = t("Skill or Skill Name is required");
    }

    if (!skillPointData.points.trim()) {
      newErrors.points = t("Point is required");
    } else {
      const points = parseInt(skillPointData.points);
      if (isNaN(points) || points < 0 || points > 10) {
        newErrors.points = t("Point must be between 0 and 10");
      }
    }

    if (!skillPointData.experienceyear.trim()) {
      newErrors.experienceyear = t("Total Experience is required");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  //function for get all category
  const getAllSkills = async () => {
    try {
      const parentSlug = "skills";

      const response = await getAllCategories(ecosystemSlug, parentSlug);

      setAllSkills(response);
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for add block
  const addSkillPointHandler = () => {
    if (validateForm()) {
      setSkillPointListBlock([...skillPointListBlock, skillPointData]);
      closeModalHandler();
    }
  };

  //function for edit education block
  const editSkillpointBlockHandler = () => {
    if (validateForm()) {
      const updatedSkillPointList = [...skillPointListBlock];
      updatedSkillPointList[skillPointIndex] = skillPointData;
      setSkillPointListBlock(updatedSkillPointList);
      closeModalHandler();
    }
  };

  //close modal handler
  const closeModalHandler = () => {
    setSkillPointData({
      skill: null,
      skillvalue: null,
      skillname: "",
      points: "",
      experienceyear: "",
    });
    setSkillPointIndex(null);
    setErrors({});

    // close the modal of addLabel
    const bootstrapModal = document.querySelector("#addSkillPointModal");
    const modal = bootstrap.Modal.getInstance(bootstrapModal);
    modal.hide();
  };

  useEffect(() => {
    getAllSkills();
  }, []);

  // Custom styles for CreatableSelect
  const customStyles = {
    control: (base, state) => ({
      ...base,
      borderColor: errors.skill || errors.skillname ? "#dc3545" : "#ced4da",
      "&:hover": {
        borderColor: errors.skill || errors.skillname ? "#dc3545" : "#ced4da",
      },
      minHeight: "38px",
      boxShadow: "none",
    }),
    placeholder: (base) => ({
      ...base,
      fontSize: "0.875rem",
      color: "#6c757d",
    }),
    input: (base) => ({
      ...base,
      fontSize: "0.875rem",
    }),
    singleValue: (base) => ({
      ...base,
      fontSize: "0.875rem",
    }),
  };

  return (
    <div
      className="modal fade"
      id="addSkillPointModal"
      tabIndex="-1"
      aria-labelledby="addEducationModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content bg-white border-0 rounded-15">
          {/* ------ modal head section start ----- */}
          <div className="modal-header p-4 pb-0 border-0">
            {/* ------ modal title start ----- */}
            <h2 className="fw-bold mb-0" id="addEducationModalLabel">
              {t("Save SkillPoint")}
            </h2>
            {/* ------ modal title end ----- */}
            {/* ------ modal close button start ----- */}
            <button
              type="button"
              className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
              aria-label="Close"
              onClick={closeModalHandler}
            ></button>
            {/* ------ modal close button end ----- */}
          </div>
          {/* ------ modal head section end ----- */}
          {/* ------ modal body section start ----- */}
          <div className="modal-body p-4">
            <form onSubmit={(e) => e.preventDefault()}>
              {/* ------ skill selection section start ----- */}

              <div className="form-group mb-4">
                <label
                  htmlFor="selectTags"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Select Skill")}
                </label>
                <Select
                  isClearable
                  placeholder={t("Select Skill")}
                  options={allSkills}
                  value={skillPointData.skillvalue}
                  styles={customStyles}
                  onChange={(val) => {
                    if (val) {
                      setSkillPointData((prevData) => ({
                        ...prevData,
                        skillvalue: val,
                        skill: val.value,
                        skillname: val.label,
                      }));
                    } else {
                      setSkillPointData((prevData) => ({
                        ...prevData,
                        skillvalue: null,
                        skill: null,
                        skillname: "",
                      }));
                    }
                    if (errors.skill || errors.skillname) {
                      setErrors((prev) => ({
                        ...prev,
                        skill: "",
                        skillname: "",
                      }));
                    }
                  }}
                  className={errors.skill ? "is-invalid-select" : ""}
                />
                {errors.skill && (
                  <div className="invalid-feedback d-block">{errors.skill}</div>
                )}
              </div>

              {/* ------ skill selection section end ----- */}

              {/* ------ skill name section start ----- */}
              {skillPointData.skillvalue ? null : (
                <div className="form-group mb-4">
                  <label
                    htmlFor="university"
                    className="d-block fs-sm fw-semibold mb-2"
                  >
                    {t("Skill Name (* if skill is not in the list)")}
                  </label>
                  <input
                    type="text"
                    className={`form-control fs-sm shadow-none ${
                      errors.skillname ? "is-invalid" : ""
                    }`}
                    placeholder={t("Skill Name")}
                    value={skillPointData.skillname}
                    onChange={(e) => {
                      setSkillPointData((prevData) => ({
                        ...prevData,
                        skillvalue: null,
                        skill: null,
                        skillname: e.target.value,
                      }));
                      if (errors.skillname || errors.skill) {
                        setErrors((prev) => ({
                          ...prev,
                          skillname: "",
                          skill: "",
                        }));
                      }
                    }}
                  />
                  {errors.skillname && (
                    <div className="invalid-feedback">{errors.skillname}</div>
                  )}
                </div>
              )}
              {/* ------ skill name section end ----- */}

              {/* ------ point section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="university"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Point (score between 0 - 10)")}
                </label>
                <input
                  type="number"
                  className={`form-control fs-sm shadow-none ${
                    errors.points ? "is-invalid" : ""
                  }`}
                  placeholder={t("Point")}
                  max="10"
                  value={skillPointData.points}
                  onChange={(e) => {
                    setSkillPointData((prevData) => ({
                      ...prevData,
                      points: e.target.value,
                    }));
                    if (errors.points) {
                      setErrors((prev) => ({ ...prev, points: "" }));
                    }
                  }}
                />
                {errors.points && (
                  <div className="invalid-feedback">{errors.points}</div>
                )}
              </div>
              {/* ------ point section end ----- */}

              {/* ------ point section start ----- */}
              <div className="form-group mb-4">
                <label
                  htmlFor="university"
                  className="d-block fs-sm fw-semibold mb-2"
                >
                  {t("Total Experience")}
                </label>
                <input
                  type="number"
                  className={`form-control fs-sm shadow-none ${
                    errors.experienceyear ? "is-invalid" : ""
                  }`}
                  placeholder={t("Total Experience")}
                  value={skillPointData.experienceyear}
                  onChange={(e) => {
                    setSkillPointData((prevData) => ({
                      ...prevData,
                      experienceyear: e.target.value,
                    }));
                    if (errors.experienceyear) {
                      setErrors((prev) => ({ ...prev, experienceyear: "" }));
                    }
                  }}
                />
                {errors.experienceyear && (
                  <div className="invalid-feedback">
                    {errors.experienceyear}
                  </div>
                )}
              </div>
              {/* ------ point section end ----- */}
              {/* ------ add/edit button start ----- */}
              <div className="action">
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={
                    skillPointIndex != null
                      ? editSkillpointBlockHandler
                      : addSkillPointHandler
                  }
                >
                  {t("Save SkillPoint")}
                </button>
              </div>
              {/* ------ add/edit button end ----- */}
            </form>
          </div>
          {/* ------ modal body section end ----- */}
        </div>
      </div>
    </div>
  );
};
export default AddEditSkillPointModal;
