/*****common-css*****/
:root{
    --bs-font-sans-serif: 'Titillium Web', sans-serif;
    --bs-body-line-height: 1.625rem;
    --bs-body-color: var(--bs-black);
    --bs-link-color-rgb: 0, 0, 0;
    --bs-link-hover-color-rgb: var(--bs-primary-rgb);
    --bs-primary: #EE523C;
    --bs-body-bg: #F2F4F6;
    --bs-primary-ylw: #FFE4A3;
    --bs-light-ylw:  #FFF8E6;
    --bs-success: #31A61E;
    --bs-sky-green: #00DEA3;
    --bs-danger: #EF3B3B;
    --bs-orange: #EE523C;
    --bs-warning: #FBBB21;
    --bs-red: #B30000;
    --bs-blue: #4285F4;
    --bs-yellow: #FB8F04;
    --bs-gray: #757B7F;
    --bs-white: #ffffff;
    --bs-gray-200: #F5F5F5;
    --bs-gray-300: #DDD;
    --bs-gray-400: #8E8EA6;
    --bs-gray-500: #6F7A7C;
    --bs-gray-600: #838689;
    --bs-extra-gray: #C4CED3;
    --bs-primary-rgb: 238, 82, 60;
    --bs-success-rgb: 49, 166, 30;
    --bs-danger-rgb: 239, 59, 59;
    --bs-warning-rgb: 251, 187, 33;
    --gredient-light: linear-gradient(90deg, #F7E6E4 0%, #E1E3FA 100%);
    --gradient-dark: linear-gradient(90deg, #FF6C65 0.10%, #F90 100%), #FFF;
    --bs-color-EA: #FFEAEA;
    font-size: 0.875rem;
}
h1, .h1, h2, .h2, h3, .h3, h4, .h4{font-weight: 600;}
h1, .h1{font-size: 3.75rem;line-height: 4.375rem;margin-bottom: 1.875rem;}
h2, .h2{font-size: 1.75rem;line-height: 2rem;letter-spacing: -1px;margin-bottom: 1.563rem;}
h3, .h3{font-size: 1.125rem;margin-bottom: 1.25rem;margin-bottom: 0.938rem;}
h4, .h4{font-size: 0.938rem;margin-bottom: 0.938rem;}
p + h3, ul + h3, ol + h3{margin-top: 1.875rem;}
a{text-decoration: none;transition: all 0.4s ease-in-out;-webkit-transition: all 0.4s ease-in-out;-moz-transition: all 0.4s ease-in-out;-ms-transition: all 0.4s ease-in-out;-o-transition: all 0.4s ease-in-out;}
p:last-of-type{margin-bottom: 0;}
ul{padding-left: 0;list-style-type: none;margin-bottom: 0;}
ol{padding-left: 1.063rem;margin: 0;}
img{vertical-align: unset;}
.shadow-sm{box-shadow: 0 2px 4px rgba(var(--bs-black-rgb), 0.1) !important;-webkit-box-shadow: 0 2px 4px rgba(var(--bs-black-rgb), 0.1) !important;}
.shadow-md{box-shadow: 0 1px 6px rgba(62, 96, 147, 0.18) !important;-webkit-box-shadow: 0 1px 6px rgba(62, 96, 147, 0.18) !important;}
.shadow-lg{box-shadow: 0 2px 14px rgba(#1D1C4C, 8.96) !important;-webkit-box-shadow: 0 2px 14px rgba(#1D1C4C, 8.96) !important;}
.btn-close{--bs-btn-close-opacity: 1;}
/*input*/
.form-control, .form-select{line-height: 1.625rem;padding: 0.625rem 1rem;border-color: var(--bs-body-bg);border-radius: 0.5rem;}
.form-control:focus, .form-select:focus{border-color: var(--bs-primary);}
.field-with-icon.icon-left .form-control{padding-left: 2.313rem;}
.field-with-icon.icon-right .form-control{padding-right: 2.313rem;}
.form-check{min-height: unset;}
.form-check input[type="checkbox"]:not([role="switch"]){width: 1.25rem;height: 1.25rem;float: none;cursor: pointer;}
.form-check input[type="checkbox"]:checked{background-color: var(--bs-primary) !important;border-color: var(--bs-primary) !important;}
.form-check input[type="checkbox"][role="switch"]{width: 3rem;height: 1.5rem;border: 0;background-color: var(--bs-gray-300);--bs-form-switch-bg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' viewBox='0 -960 960 960' width='48' fill='%23fff'%3E%3Cpath d='M480-80q-82 0-155-31.5t-127.5-86Q143-252 111.5-325T80-480q0-83 31.5-156t86-127Q252-817 325-848.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 82-31.5 155T763-197.5q-54 54.5-127 86T480-80Z'/%3E%3C/svg%3E");}
.form-control.not-valid{border-color: var(--bs-primary);background-color: #FFF9F9;color: var(--bs-primary);}
.not-valid + .field-icon{color: var(--bs-primary) !important;}
/*input*/
/*btn*/
.btn{--bs-btn-line-height: 1.625rem;--bs-btn-font-weight: 600;--bs-btn-padding-x: 0.625rem;--bs-btn-padding-y: 0.375rem;--bs-btn-border-radius: 0.625rem;}
.btn-primary{--bs-btn-bg: var(--bs-primary);--bs-btn-border-color: var(--bs-primary);--bs-btn-hover-color: var(--bs-white);--bs-btn-hover-bg: rgba(var(--bs-primary-rgb), 0.8);--bs-btn-hover-border-color: rgba(var(--bs-primary-rgb), 0.8);--bs-btn-active-color: var(--bs-white);--bs-btn-active-bg: rgba(var(--bs-primary-rgb), 0.8);--bs-btn-active-border-color: rgba(var(--bs-primary-rgb), 0.8);--bs-btn-disabled-bg: var(--bs-primary);--bs-btn-disabled-border-color: var(--bs-primary);}
.btn-outline-primary{--bs-btn-color: var(--bs-primary);--bs-btn-border-color: var(--bs-primary);--bs-btn-hover-color: var(--bs-primary);--bs-btn-hover-bg: rgba(var(--bs-primary-rgb), 0.1);--bs-btn-hover-border-color: var(--bs-primary);--bs-btn-active-color: var(--bs-primary);--bs-btn-active-bg: rgba(var(--bs-primary-rgb), 0.1);--bs-btn-active-border-color: var(--bs-primary);--bs-btn-disabled-color: var(--bs-primary);--bs-btn-disabled-border-color: var(--bs-primary);}
.btn.btn-gray{background-color: var(--bs-body-bg);border-color: var(--bs-body-bg);color: var(--bs-black);}
.btn.btn-gray:hover, .btn.btn-gray:active{background-color: var(--bs-gray-300);border-color: var(--bs-gray-300);}
/*btn*/
/*modal*/
.btn-close{width: 2.75rem;height: 2.75rem;--bs-btn-close-hover-opacity: 1;transition: all 0.3s ease-in-out;}
.btn-close:hover{background-color: var(--bs-primary);}
/*modal*/
/*accordion*/
.accordion .accordion-item:not(:last-child){padding-bottom: 1.875rem;margin-bottom: 1.875rem;border-bottom: 1px solid var(--bs-body-bg) !important;}
.accordion-button{font-size: 1.125rem;font-weight: 600;--bs-accordion-active-color: var(--bs-black);}
/*accordion*/
/*progress*/
.progress, .progress-stacked{--bs-progress-bg: var(--bs-gray-300);--bs-progress-bar-bg: var(--bs-primary);}
.circle-progress{transform: rotate(-90deg);}
.circle-progress .percent{stroke-dasharray: 100;stroke-dashoffset: 100;stroke-linecap: round;}
.marked::before{content: '';display: block;width: 0.375rem;height: 0.375rem;flex-shrink: 0;border-radius: 50%;}
.marked.completed::before{background-color: var(--bs-success);}
.marked.on-track::before, .marked.in-progress::before{background-color: var(--bs-blue);}
.marked.delayed::before, .marked.pending::before{background-color: var(--bs-yellow);}
.marked.at-risk::before{background-color: var(--bs-primary);}
.marked.overdue::before{background-color: var(--bs-red);}
/*progress*/
/*helper classes*/
.fs-xs{font-size: 0.75rem;line-height: 1.375rem;}.fs-sm{font-size: 0.875rem;line-height: 1.5rem;}.fs-md{font-size: 0.938rem;line-height: 1.563rem;}.fs-lg{font-size: 1.125rem;line-height: 1.75rem;}
.text-gray{color: var(--bs-gray);}.text-gray-300{color: var(--bs-gray-300);}.text-gray-400{color: var(--bs-gray-400);}.text-gray-500{color: var(--bs-gray-500);}.text-gray-600{color: var(--bs-gray-600);}.text-orange{color: var(--bs-orange);}
.bg-gray{background-color: var(--bs-gray);}.bg-gray-300{background-color: var(--bs-gray-300);}
.hover-primary:hover{color: var(--bs-primary);}
.body-bg{background-color: var(--bs-body-bg);}.bg-gray-200{background-color: var(--bs-gray-200);}
.icon-lg{font-size: 1.25rem;}.icon-md{font-size: 1.125rem;}.icon-sm{font-size: 1rem;}
.icon-fill{font-variation-settings: 'FILL' 1, 'wght' 400, 'GRAD' 0, 'opsz' 48}
.border-gray-300{border-color: var(--bs-gray-300) !important;}.border-gray-600{border-color: var(--bs-gray-600) !important;}.border-gray-bg{border-color: var(--bs-body-bg) !important;}
.border-solid{border-style: solid !important;}.border-dashed{border-style: dashed !important;}
.rounded-5{border-radius: 0.313rem !important;}.rounded-8{border-radius: 0.5rem;}.rounded-10{border-radius: 0.625rem;}.rounded-15{border-radius: 0.938rem;}.rounded-18{border-radius: 1.125rem;}.rounded-90{border-radius: 5.625rem;}
.object-center{object-position: center;}
.bg-cover{background-size: cover;}.bg-norepeat{background-repeat: no-repeat;}.bg-center{background-position: center;}
.gradient-light{background: var(--gredient-light);}.gradient-dark{background: var(--gradient-dark);}
.cursor-pointer{cursor: pointer;}
.z-index-2{z-index: 2;}
.back-btn{width: 2.25rem;height: 2.25rem;}
.back-btn:hover{background-color: var(--bs-primary);color: var(--bs-white);}
.hide-dropdown-arrow::after{display: none;}
/* .min-h-screen{min-height: 100vh;} */
.flex-shrink-0{flex-shrink: 0;}
.notification-dot{width: 0.5rem;height: 0.5rem;}
.preview-uploaded-files .file{width: 9.375rem;height: 9.375rem;}
.avatar-collage li:not(:first-child){margin-left: -0.5rem;}
.min-w-auto{min-width: auto !important;}
.dropdown-arrow-white::before{color: var(--bs-white) !important;}
/*helper classes*/
/*dropdown*/
.dropdown-item{font-weight: inherit;}
/*dropdown*/
/*tab*/
ul.secondary-menu li.nav-item a:hover, ul.secondary-menu li.nav-item a.active, .nav-tabs li a.active, .nav-tabs li a:hover{color: var(--bs-primary) !important;}
ul.secondary-menu li.nav-item a.active::before{content: '';display: block;width: 100%;height: 0.125rem;background-color: var(--bs-primary);position: absolute;left: 0;right: 0;bottom: 0;}
/*tab*/
/*table*/
table.table{--bs-table-bg: var(--bs-body-bg);--bs-table-border-color: rgba(0, 0, 0, 0.1);border-collapse: separate;border-spacing: 0;border-vertical-spacing: 5px;-webkit-border-vertical-spacing: 5px;}
table.table thead tr th::before, table.table thead tr th{position: relative;cursor: pointer;}
table.table thead tr th::before, table.table thead tr th::after{font-family: 'Material Symbols Outlined';font-size: 1rem;opacity: 0.5;position: absolute;right: 0.938rem;}
table.table thead tr th::before{content: 'keyboard_arrow_up';top: 0.625rem;}
table.table thead tr th::after{content: 'keyboard_arrow_down';bottom: 0.625rem;}
table.table.no-arrow thead tr th::before, table.table thead tr th::after{display: none;}
table.table thead tr th.sorting_asc::before, table.table thead tr th.sorting_desc::after{opacity: 1;}
table.table thead tr th.sorting_desc::before, table.table thead tr th.sorting_asc::after{opacity: 0.5;}
table.table thead tr th, table.table tbody tr td{padding: 0.938rem 2.813rem 0.938rem 0.938rem;border-color: var(--bs-table-border-color);}
table.table tbody tr td.separator{position: relative;}
table.table tbody tr td.separator::after{content: '';display: block;width: 1px;height: 1.5rem;background-color: var(--bs-gray-500);opacity: 0.5;position: absolute;top: 50%;right: 0;margin-top: -0.75rem;}
.table-wrapper table.table thead tr{background: var(--gredient-light);}
.table-wrapper table.table thead tr th:first-child, .table-wrapper table.table tbody tr td:first-child{border-top-left-radius: 0.625rem;border-bottom-left-radius: 0.625rem;}
.table-wrapper table.table thead tr th:last-child, .table-wrapper table.table tbody tr td:last-child{border-top-right-radius: 0.625rem;border-bottom-right-radius: 0.625rem;}
.table-wrapper table.table tbody tr td .dropdown .dropdown-toggle{width: 1.875rem;height: 1.875rem;}
.table-wrapper table.table tbody tr td .dropdown .dropdown-toggle:hover{background-color: var(--bs-white) !important;}
.table-wrapper table.table tbody tr td .dropdown .dropdown-toggle::after{display: none;}
/*table*/
/*range slider*/
.ui-widget.ui-slider.ui-slider-horizontal{height: 0.25rem;}
.ui-widget.ui-slider .ui-slider-range{background-color: var(--bs-primary);transition: all 0.3s ease-in-out;}
.ui-widget.ui-slider .ui-slider-handle{width: 0.938rem;height: 0.938rem;background-color: var(--bs-white);border: 1px solid var(--bs-primary);border-radius: 50%;top: -0.344rem;margin-left: -0.469rem;outline: none;transition: all 0.3s ease-in-out;}
.ui-widget.ui-slider .ui-slider-handle.ui-state-hover{background-color: var(--bs-primary);}
.ui-widget.ui-slider .ui-slider-handle.ui-state-active{transform: scale(1.4);}
/*range slider*/
/*tags*/
.tags .tag label:hover{color: var(--bs-primary);border-color: rgba(var(--bs-primary-rgb), 0.26) !important;transition: all 0.3s ease-in-out;}
.tags .tag input[type="checkbox"]:checked + label{background: rgba(var(--bs-primary-rgb), 0.26) !important;color: var(--bs-primary);border-color: rgba(var(--bs-primary-rgb), 0.26) !important;}
/*tags*/
/*breadcrumb*/
.breadcrumb-container .breadcrumb{--bs-breadcrumb-divider-color: var(--bs-gray-500);--bs-breadcrumb-item-active-color: var(--bs-gray-500);}
/*breadcrumb*/
/*filter*/
.filter-container .dropdown .dropdown-toggle{padding-right: 2.375rem;}
.filter-container .dropdown .dropdown-toggle::after{display: none;}
.filter-container .dropdown .dropdown-toggle::before{content: 'expand_more';display: inline-block;font-family: 'Material Symbols Outlined';font-weight: normal;font-style: normal;font-size: 24px;line-height: 1;color: var(--bs-gray);position: absolute;top: 50%;right: 0.625rem;transform: translateY(-50%);}
/*filter*/
/*pagination*/
.pagination li a, .pagination li > span{width: 2rem;height: 2rem;line-height: 2rem;}
.pagination li.active span{background-color: var(--bs-primary);color: var(--bs-white);}
/*pagination*/
/*offcanvas*/
.offcanvas.md{--bs-offcanvas-width: 51.25rem;}.offcanvas.lg{--bs-offcanvas-width: 90%;}
.offcanvas-title{font-size: 1.563rem;line-height: 1;font-weight: 600;}
/*offcanvas*/
/*upload-files*/
.upload-files .file{min-width: 8.438rem;min-height: 8.438rem;}
.upload-files .file img{max-width: 6.25rem;}
/*upload-files*/
/*select2*/
.select2-container{display: block;max-width: 100%;}
.select2-container .select2-selection{display: flex;flex-wrap: wrap;gap: 0.313rem;padding: 0.563rem 1rem;height: auto;min-height: unset;background-color: var(--bs-body-bg);border-color: var(--bs-body-bg);border-radius: 0.5rem;}
.select2-container .select2-selection.select2-selection--multiple{padding: 0.375rem 1rem;}
.select2-container.select2-container--focus .select2-selection{border-color: var(--bs-primary);}
.select2-container .select2-selection .select2-selection__rendered{display: flex;flex-wrap: wrap;gap: 0.313rem;line-height: inherit;padding: 0;}
.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice{display: flex;align-items: center;gap: 0.313rem;background-color: var(--bs-white);font-size: 0.875rem;line-height: 1.125rem;font-weight: 600;padding: 0.375rem 0.625rem;border: 0;border-radius: 0.313rem;margin: 0;}
.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove{font-family: 'Material Symbols Outlined';font-size: 1.25rem;line-height: 1;position: static;background: transparent;padding: 0;border: 0;}
.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove::before{content: 'close';}
.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove span{display: none;}
.select2-container .select2-selection .select2-selection__rendered .select2-selection__choice .select2-selection__choice__display{padding: 0;}
.select2-container .select2-selection .select2-selection__arrow{width: auto;height: auto;position: static;margin-left: auto;    font-family: 'Material Symbols Outlined';font-weight: normal;font-style: normal;font-size: 24px;line-height: 1;letter-spacing: normal;text-transform: none;display: inline-block;white-space: nowrap;word-wrap: normal;direction: ltr;-webkit-font-feature-settings: 'liga';-webkit-font-smoothing: antialiased;color: var(--bs-gray-400)}
.select2-container .select2-selection .select2-selection__arrow::before{content: 'expand_more';}
.select2-container .select2-selection .select2-selection__arrow b{display: none;}
.select2-container .select2-dropdown{border: 0;border-radius: 0.625rem;overflow: hidden;box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10), 0px 18px 40px 0px rgba(21, 21, 35, 0.34);}
.select2-container .select2-dropdown .select2-search{padding: 0.938rem;}
.select2-container .select2-dropdown .select2-results{font-size: 0.938rem;line-height: 1.563rem;font-weight: 600;}
.select2-container .select2-dropdown .select2-results .select2-results__option{padding: 0.313rem 0.938rem;}
.select2-container .select2-dropdown .select2-results .select2-results__option.select2-results__option--highlighted.select2-results__option--selectable{background-color: var(--bs-primary);}
.select2-container .select2-dropdown .select2-results .select2-results__option.select2-results__option--selected{background-color: var(--bs-body-bg);}
.select2-container .select2-dropdown .select2-results .select2-results__option .img{width: 1.5rem;height: 1.5rem;}
/*select2*/
/*****common-css*****/
/*****authentication*****/
.authentication-wrapper .authentication-container{width: 28.75rem;}
.authentication-wrapper.register .authentication-container{width: 37.5rem;}
/*****authentication*****/
/*****header*****/
header#header nav.navbar{padding: 0.781rem 0;}
header#header nav.navbar .navbar-brand img{max-height: 1.875rem;}
header#header nav.navbar form input.form-control{min-width: 18.75rem;padding-top: 0.813rem;padding-bottom: 0.813rem;}
header#header nav.navbar .navbar-nav li.nav-item.notification .has-notification{width: 0.625rem;height: 0.625rem;top: 2px;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-toggle::after{display: none;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-toggle .avatar{width: 2.25rem;height: 2.25rem;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu{--bs-dropdown-min-width: 15.625rem;margin-top: 1.25rem;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu .profile .avatar{width: 1.875rem;height: 1.875rem;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu .logout{width: 1.875rem;height: 1.875rem;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu li > a:hover{gap: 1.5rem !important;color: var(--bs-primary) !important;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu li > a.active{color: var(--bs-primary) !important;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu li .language-selector .language.active{background: var(--gredient-light);}
header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu{--bs-dropdown-min-width: 23.75rem; max-height: 30rem;overflow-y: auto;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu  li{background-color: var(--bs-white);}
header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu  li.read{background-color: var(--bs-gray-200);}
header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu  li.task_complete_list h6{text-decoration-line: line-through;text-decoration-thickness: 2px;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown.notification .dropdown-menu  li:not(:last-child){margin-bottom: 0.313rem;}
header#header .offcanvas{--bs-offcanvas-width: 16.25rem;}
header#header .offcanvas .offcanvas-header .btn-close{width: 2.25rem;height: 2.25rem;background-image: none;}
header#header .offcanvas .offcanvas-header .btn-close:hover{background-color: var(--bs-primary) !important;color: var(--bs-white);}
header#header .offcanvas .offcanvas-header .user .avatar{width: 2.25rem;height: 2.25rem;}
header#header .offcanvas .offcanvas-body  ul.nav li a{padding: 0.75rem 0.938rem;position: relative;}
header#header .offcanvas .offcanvas-body  ul.nav li a::before{content: '';display: block;width: 0;height: 100%;background-color: var(--bs-body-bg);border-radius: 0.625rem;position: absolute;top: 0;bottom: 0;left: 0;z-index: -1;transition: all 0.3s ease-in-out;}
header#header .offcanvas .offcanvas-body  ul.nav li a:hover::before{width: 100%;}
header#header .offcanvas .offcanvas-body  ul.nav li a.active::before{width: 100%;background-color: var(--bs-primary);}
header#header .offcanvas .offcanvas-body  ul.nav li a.active{color: var(--bs-white) !important;}
header#header .offcanvas .offcanvas-body  ul.nav li .submenu{padding-left: 2.5rem;opacity: 0;visibility: hidden;height: 0;transition: all 0.3s ease-in-out;}
header#header .offcanvas .offcanvas-body  ul.nav li.opened .submenu{opacity: 1;visibility: visible;height: auto;}
header#header .offcanvas .offcanvas-body  ul.nav li .submenu li a{padding: 0.875rem 0.938rem;}
header#header .offcanvas .offcanvas-body  ul.nav li .submenu li a.active{color: var(--bs-primary) !important;}
header#header .offcanvas .offcanvas-body  ul.nav li .submenu li a.active::before{background-color: transparent;}

.navbar  .task_list{max-height: 350px;overflow-y: auto;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu .task_complete_list a:hover{color: var(--bs-white) !important;}
header#header nav.navbar .navbar-nav li.nav-item.dropdown .dropdown-menu .task_complete_list .dropdown-menu{position: static !important;border: 0;}
/*****header*****/
/*****dashboard*****/
.dashboard-wrapper .tab-links-container .nav-tabs li a.active::before{display: none;}
.dashboard-wrapper .tab-container .card-content .head .icon{width: 4.375rem;height: 4.375rem;flex: 0 0 auto;}
.dashboard-wrapper .tab-container .card-content .head .icon .material-symbols-outlined{font-size: 2.375rem;}
.dashboard-wrapper .tab-container .card-content .head .filter .form-select{font-size: 0.875rem;line-height: 1;padding: 0.625rem 1.625rem 0.625rem 0.5rem;background-position: right 0.313rem center;background-size: 0.625rem;}
.dashboard-wrapper .tab-container .card-content .items:not(:last-of-type){padding-bottom: 1rem;margin-bottom: 1rem;border-bottom: 1px solid var(--bs-body-bg);}
.dashboard-wrapper .tab-container .ranking-container .card-content .meta li:not(:last-child){padding-right: 0.625rem;margin-right: 0.625rem;border-right: 1px solid rgba(0, 0, 0, 0.1);}
.dashboard-wrapper .tab-container table.table{--bs-table-bg: var(--bs-white);}
.dashboard-wrapper .tab-container table.table.dataTable thead tr th{color: var(--bs-gray-600);}
/*****dashboard*****/
/*****crm*****/
.crm-contact-timeline-wrapper .timeline > li ul li{padding: 0 0 1.875rem 2.625rem;}
.crm-contact-timeline-wrapper .timeline > li ul li::before{content: '';display: block;width: 1px;background-color: var(--bs-gray-300);position: absolute;left: 0;left: 1rem;top: 2.625rem;bottom: 0.625rem;}
.crm-contact-timeline-wrapper .timeline > li:last-child ul li:last-child::before{display: none;}
.crm-contact-mail-wrapper .filter-container #mailTab li a, .crm-conversation-wrapper ul#mailTab li a{min-width: 8.75rem;padding: 0.875rem 0.938rem;}
.crm-contact-mail-wrapper .filter-container #mailTab li a.active, .crm-conversation-wrapper ul#mailTab li a.active{background-color: var(--bs-primary);color: var(--bs-white) !important;}
.crm-conversation-wrapper .table-wrapper .table tbody tr td.mail-details{max-width: 780px;}
.crm-conversation-wrapper .contacts-container .contacts .contact.selected{background-color: var(--bs-gray-200);}
.crm-conversation-wrapper .contacts-container .contacts .contact .contact-content .avatar{width: 2.625rem;height: 2.625rem;}
.crm-conversation-wrapper .contacts-container .contacts .contact.group .contact-content .avatar .images{gap: 0.125rem;}
.crm-conversation-wrapper .contacts-container .contacts .contact.group .contact-content .avatar .images > *{width: 1.25rem;height: 1.25rem;}
.crm-conversation-wrapper .contacts-container .contacts .contact .contact-content .time .message-count{width: 1.125rem;height: 1.125rem;}
/*****crm*****/
/*****project-management*****/
.project-wrapper .table-wrapper table tbody tr td.rank input{max-width: 5rem;}
.projects-wrapper .project-tasks-container .accordion .accordion-item{padding-bottom: 0;border-bottom: 0 !important;}
.projects-wrapper .project-tasks-container .accordion .accordion-item .accordion-header .accordion-button{flex-direction: row-reverse;gap: 1rem;}
.projects-wrapper .project-tasks-container .accordion .accordion-item .accordion-header .action a:hover{color: var(--bs-primary) !important;}
.projects-wrapper .project-tasks-container  table.table tbody tr.completed td{color: var(--bs-gray);background-color: transparent;border-width: 1px 0 1px 0 !important;border-style: solid !important;border-color: var(--bs-body-bg) !important;}
.projects-wrapper .project-tasks-container  table.table tbody tr.completed td:first-child{border-left: 1px solid var(--bs-body-bg) !important;}
.projects-wrapper .project-tasks-container  table.table tbody tr.completed td:last-child{border-right: 1px solid var(--bs-body-bg) !important;}
.projects-wrapper .project-tasks-container.grid-view .single-card .title .dropdown button::after{display: none;}
.addTask .nav-tabs li a.active::before{content: '';display: block;width: 100%;height: 0.125rem;background-color: var(--bs-primary);position: absolute;left: 0;right: 0;bottom: 0;}
.addTask .logs .log:not(:last-child){margin-bottom: 1rem;}
/*****project-management*****/
/*****challenges*****/
.challenges-wrapper .table-wrapper table tbody tr td.rank input{width: 5rem;}
.challenges-wrapper .table-wrapper table tbody tr td.v-chat .notification-dot, .challenges-wrapper .table-wrapper table tbody tr td.c-chat .notification-dot{top: -2.5px;right: -2.5px;}
.challenges-wrapper .job-meta li:not(:last-child){padding-right: 0.625rem;margin-right: 0.625rem;}
.challenges-wrapper .job-meta li:not(:last-child)::after{content: '';display: block;width: 0.25rem;height: 0.25rem;background-color: var(--bs-gray);border-radius: 50%;position: absolute;top: 0.625rem;right: -0.125rem;}
.challenges-wrapper .hiring-process-flow{counter-reset: process-counter;}
.challenges-wrapper .hiring-process-flow .step{padding: 0 0 1.875rem 3.188rem;}
.challenges-wrapper .hiring-process-flow .step:last-child{padding-bottom: 0;}
.challenges-wrapper .hiring-process-flow .step::before{content: counter(process-counter);counter-increment: process-counter;display: block;width: 2.25rem;height: 2.25rem;line-height: 2rem;font-weight: 600;color: var(--bs-gray);text-align: center;background-color: var(--bs-white);border: 2px solid var(--bs-gray);border-radius: 50%;position: absolute;top: -0.25rem;left: 0;z-index: 2;}
.challenges-wrapper .hiring-process-flow .step.active::before{color: var(--bs-primary);border-color: var(--bs-primary);}
.challenges-wrapper .hiring-process-flow .step:not(:last-child)::after{content: '';display: block;width: 2px;height: 100%;background-color: var(--bs-gray);position: absolute;top: 0;bottom: 0;left: 1.063rem;}
.challenges-wrapper .hiring-process-flow .step.active::after{background-color: var(--bs-primary);}
.challenges-wrapper .responses-card-wrapper .column-head .dropdown button::after{display: none;}
.challenges-wrapper .responses-card-wrapper .column-head .dropdown .choose-color{--bs-dropdown-min-width: 11.25rem}
.challenges-wrapper .responses-card-wrapper .column-head .dropdown .choose-color li a{width: 1.875rem;height: 1.875rem;}
#offcanvasresponseDetails .offcanvas-header img{max-height: 1.25rem;}
#offcanvasresponseDetails .offcanvas-header ul li:not(:last-child){padding-right: 0.938rem;margin-right: 0.938rem;}
#offcanvasresponseDetails .offcanvas-header ul li:not(:last-child)::after{content: '';display: block;width: 0.375rem;height: 0.375rem;background-color: rgba(0, 0, 0, 0.2);border-radius: 50%;position: absolute;top: 0.563rem;right: -0.188rem;}
#kanbanExample .menu li a{padding: 0.625rem 1rem;border-top-right-radius: 0.5rem;border-bottom-right-radius: 0.5rem;}
#kanbanExample .menu li.active a{background: var(--gredient-light);color: var(--bs-primary);}
/*****challenges*****/
/*****survey*****/
.survey-wrapper .table-wrapper table tbody tr td.responces .notification-dot{top: -2.5px;right: -2.5px;}
/*****survey*****/
/*****events*****/
#eventType form input[type="radio"]:checked + .type{border-color: var(--bs-primary) !important;}
/*****events*****/
/*****company*****/
.company-wrapper .table-wrapper table tbody tr td.company-name .company-info img{max-height: 1.375rem;}
/*****company*****/
/*****group*****/
.group-details-wrapper .categories-container ul li a.active, .group-details-wrapper .categories-container ul li a:hover{color: var(--bs-black);}
/*****group*****/
/*****experts*****/
.group-details-wrapper .work-experience-flow{counter-reset: experience-counter;}
.group-details-wrapper .work-experience-flow .step{padding: 0 0 1.875rem 3.188rem;}
.group-details-wrapper .work-experience-flow .step:last-child{padding-bottom: 0;}
.group-details-wrapper .work-experience-flow .step::before{content: counter(experience-counter);counter-increment: experience-counter;display: block;width: 2.25rem;height: 2.25rem;line-height: 2rem;font-weight: 600;color: var(--bs-gray);text-align: center;background-color: var(--bs-white);border: 2px solid var(--bs-gray);border-radius: 50%;position: absolute;top: -0.25rem;left: 0;z-index: 2;}
.group-details-wrapper .work-experience-flow .step.active::before{color: var(--bs-primary);border-color: var(--bs-primary);}
.group-details-wrapper .work-experience-flow .step::after{content: '';display: block;width: 2px;height: 100%;background-color: var(--bs-gray);position: absolute;top: 0;bottom: 0;left: 1.063rem;}
.group-details-wrapper .work-experience-flow .step.active::after{background-color: var(--bs-primary);}
.group-details-wrapper .work-experience-flow .step .employment-type li:not(:last-child){padding-right: 0.938rem;margin-right: 0.938rem;}
.group-details-wrapper .work-experience-flow .step .employment-type li:not(:last-child)::before{content: '';display: block;width: 0.5rem;height: 0.5rem;background-color: var(--bs-gray-300);border-radius: 50%;position: absolute;top: 0.5rem;right: -0.25rem;}
#offcanvasAddEvent .days .day label{width: 2.5rem;height: 2.5rem;}
#offcanvasAddEvent .days .day input[type="checkbox"]:checked + label{background: var(--gredient-light);color: var(--bs-primary);}
.expert-details-wrapper .filter-container #viewTab li a{min-width: 8.75rem;padding: 0.875rem 0.938rem;}
.expert-details-wrapper .filter-container #viewTab li a.active{background-color: var(--bs-primary);color: var(--bs-white) !important;}
/*****experts*****/
/*****courses*****/
.course-details-wrapper form .action li:not(:last-child){padding-right: 0.938rem;margin-right: 0.938rem;border-right: 1px solid var(--bs-gray-300);}
#viewTask .offcanvas-header .task-category li:not(:last-child){margin-right: 0.625rem;}
#viewTask .offcanvas-header .task-category li{padding-left: 0.813rem;}
#viewTask .offcanvas-header .task-category li::before{content: '';display: block;width: 0.5rem;height: 0.5rem;background-color: rgba(0, 0, 0, 0.2);border-radius: 50%;position: absolute;left: 0;top: 0.563rem;}
/*****courses*****/
/*****offers*****/
#offcanvasOfferPost .nav-tabs li a.active::before{content: '';display: block;width: 100%;height: 0.125rem;background-color: var(--bs-primary);position: absolute;left: 0;right: 0;bottom: 0;}
/*****offers*****/
/*****marketplace*****/
.marketplace-wrapper .categories-container ul li a.active, .marketplace-wrapper .categories-container ul li a:hover{color: var(--bs-black);}
/*****marketplace*****/

/* invoice--table */

/* 03.09.2024 */
.invoice_usr{height: 23px;width: 23px;min-width: 23px;}
.invoice_usr img{max-width: 100%;max-height: 100%;}
.graph_select{width: 152px;min-width: 152px;}
.report_slect_container .form-select{width: auto;min-width: 156px;}
/* .report_table table td p{display: flex;align-items: center;gap: 5px;} */
.report_table table td p span{line-height: 100%;display: inline-block;vertical-align: middle;}

/* 03.09.2024 */

/* .table-wrapper tr td svg {transform: rotate(-90deg);height:25px;width: 25px;}
.table-wrapper tr td svg circle {
    width: 100%;
    height: 100%;
    fill: none;
    stroke: #EAEAEA;
    stroke-width: 1.5;
    stroke-linecap: round;
}
.table-wrapper tr td svg circle:last-of-type {
    stroke-dasharray: 70.6858px; 
    stroke-dashoffset: calc(70.6858px - (70.6858px * var(--percent)) / 100); 
    stroke: var(--bs-primary);
} */

.rank-value.form-control{height: 30px;color: #000;width: 50px;padding: 0 10px;}

/* ---06.09.2024-- */

.invoice-uploads li{height: 38px;}
.invoice-uploads li img{max-height: 100%;}
.invoice_right_info_list strong{min-width: 95px;}
.invoice_right_info_list input{width: 135px;}
.project_table_wrapper table th:first-child, .project_table_wrapper table td:first-child{padding-left: 16px !important;}
.condition_errorbx{border-color: var(--bs-primary) !important;}

/* 24.10.2024 */

.light-ylw-bg{background-color: var(--bs-light-ylw) !important;}  
.primary-ylw-bg{background-color: var( --bs-primary-ylw);}
.text-dark{color: var(--bs-dark);}.text-primary{color: var(--bs-primary);}.text-sky-green{color: var(--bs-sky-green);}
.bullet_graph{height: 7px;width: 7px;min-width: 7px;}
.graph_info_item{width: 48.5%;}
.graph_pic {height: 230px;}
.graph_select{min-width: 120px;}

/* 24.10.2024 */


/*****responsive-css*****/
@media(min-width: 450px){
    /*****common-css*****/
    :root{
        font-size: 16px;
    }
    h2, .h2{font-size: 1.875rem;line-height: 2.125rem;}
    /*button*/
    .btn{--bs-btn-padding-x: 0.938rem;--bs-btn-padding-y: 0.5rem;}
    /*button*/
    /*****common-css*****/
}
@media (min-width: 576px){
    /*****common-css*****/
    .position-sm-absolute{position: absolute;}
    .flex-grow-sm-0{flex-grow: 0 !important;}
    /*****common-css*****/
    /*****crm*****/
    .crm-contact-timeline-wrapper .timeline > li{padding-left: 8.125rem;}
    /*****crm*****/
    /*****success-stories*****/
    .success-story-details-wrapper .success-story-video-container .video{width: 50%;}
    /*****success-stories*****/
}
@media(min-width: 768px){
    /*****common-css*****/
    h2, .h2{font-size: 2rem;line-height: 2.25rem;}
    /*****common-css*****/
    /*****success-stories*****/
    .success-story-details-wrapper .success-story-video-container .video{width: 33.333%;}
    /*****success-stories*****/
}
@media(min-width: 992px){
    /*****common-css*****/
    h2, .h2{font-size: 2.125rem;line-height: 2.375rem;}
    .body-lg-bg{background-color: var(--bs-body-bg);}
    .border-lg-0{border-width: 0;}
    .rounded-lg-0{border-radius: 0;}
    .border-lg-start{border-left-width: 1px !important;}
    .border-lg-end{border-right-width: 1px !important;}
    .border-lg-solid{border-style: solid;}
    .border-bottom-lg-0{border-bottom-width: 0 !important;}
    .border-gray-lg-300{border-color: var(--bs-gray-300) !important;}
    /*filter*/
    .filter-container .dropdown .dropdown-toggle{min-width: 9.375rem;}
    /*filter*/
    /*****common-css*****/
    /*****header*****/
    header#header nav.navbar form input.form-control{min-width: 26.563rem;}
    /*****header*****/
    /*****group*****/
    .group-details-wrapper .form-group .upload-images li{width: 20%;}
    /*****group*****/
    /*****courses*****/
    .course-details-wrapper form .action li:not(:last-child){padding-right: 1.875rem;margin-right: 1.875rem;}
    /*****courses*****/
    /*****success-stories*****/
    .success-story-details-wrapper .success-story-video-container .video{width: 25%;}
    /*****success-stories*****/
}
@media(min-width: 1200px){
    /*****common-css*****/
    h2, .h2{font-size: 2.25rem;line-height: 2.5rem;}
    /*****common-css*****/
    /*****success-stories*****/
    .success-story-details-wrapper .success-story-video-container .video{width: 20%;}
    /*****success-stories*****/
}
@media(max-width:1365px){
    .graph_info_item{width: 100%;}
}
@media(max-width:1199px){
    .review_invoice_wrapper .colxl-8{margin-bottom: 24px;}
    .review_invoice_wrapper .colxl-8{margin-bottom: 24px;}
    .dashboard_top {flex-wrap: wrap;}
    .dashboard_top .col{flex: 1 0 30%;}
}
@media(max-width:991px){
    .invoice_information_outer .col-lg-4{margin-bottom: 20px;}
    .invoice_right_info_list input { width: 100%; }
    .project_table_wrapper table .table thead tr th, .project_table_wrapper table.table tbody tr td{min-width: 140px;}
    .pricet_select_wrapper .col-md-4{margin-bottom: 15px;}
    .pricet_select_wrapper .col-md-4:last-child{margin-bottom: 0;}
}

@media(max-width:767px){
    #offcanvasAddInvoice .btns_groups .btn{width: 100%;}
}
@media(max-width:575px){
    .graph_info_item { width: 48.5%; }
}

/*****responsive-css*****/