/* eslint-disable */
import { assetImages } from "constants";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import * as url from "helper/UrlHelper";

const CrmLeadInformationCommonHeader = ({
  moduleName = "",
  leadName = "",
  leadImage = "",
  isAuthenticatedUser = false,
}) => {
  const { t } = useTranslation();
  const params = useParams();

  // Directly read from localStorage
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess") || "";

  if (!userInfo) return null;

  // Role check functions
  const isAdminOrSuperAdmin = ["ADMIN", "SUPER_ADMIN"].includes(
    userInfo?.role?.slug
  );

  const hasOpportunityAccess =
    isAdminOrSuperAdmin || moduleAccess.includes("MOD_OPPORTUNITY");

  // Navigation items configuration
  const navItems = [
    {
      key: "leadinformation",
      path: `/admin/crm/lead/information/${params.id}`,
      label: t("Lead Information"),
      show: true,
    },
    {
      key: "timeline",
      path: `/admin/crm/lead/timeline/${params.id}`,
      label: t("Timeline"),
      show: isAdminOrSuperAdmin || isAuthenticatedUser,
    },
    {
      key: "addtask",
      path: `/admin/crm/lead/addtask/${params.id}`,
      label: t("Add Task"),
      show: isAdminOrSuperAdmin || isAuthenticatedUser,
    },
    {
      key: "communication",
      path: `/admin/crm/lead/communication/${params.id}`,
      label: t("Communication"),
      show: isAdminOrSuperAdmin || isAuthenticatedUser,
    },
    {
      key: "opportunity",
      path: `/admin/crm/lead/opportunities/${params.id}`,
      label: t("Opportunities"),
      show: hasOpportunityAccess,
    },
  ];

  const finalLeadImageSrc = leadImage
    ? url.SERVER_URL + leadImage
    : assetImages.defaultUser;

  return (
    <div className="inner-menu-container bg-white shadow-sm">
      <div className="container-fluid d-flex align-items-center gap-2 gap-sm-4 px-lg-5">
        <Link
          to="/admin/crm/lead/list"
          className="back-btn d-flex flex-shrink-0 align-items-center justify-content-center body-bg text-gray rounded-circle"
        >
          <span className="d-block material-symbols-outlined">arrow_back</span>
        </Link>

        <ul className="nav secondary-menu mw-100 flex-nowrap gap-lg-5 flex-fill fs-sm fw-semibold text-center border-0 text-nowrap overflow-auto">
          <li className="profile d-flex align-items-center gap-2 text-nowrap">
            <div
              className="avatar rounded-circle-overflow-hidden"
              style={{ width: "38px", height: "38px" }}
            >
              <img
                src={finalLeadImageSrc}
                alt={leadName}
                className="rounded-circle w-100 h-100 object-fit-cover object-center"
              />
            </div>
            <h3 className="mb-0">{leadName}</h3>
          </li>

          {navItems.map(
            (item) =>
              item.show && (
                <li className="nav-item" key={item.key}>
                  <Link
                    to={item.path}
                    className={`d-block text-black px-3 py-4 position-relative ${
                      moduleName === item.key ? "active" : ""
                    }`}
                  >
                    {item.label}
                  </Link>
                </li>
              )
          )}
        </ul>
      </div>
    </div>
  );
};

export default CrmLeadInformationCommonHeader;
