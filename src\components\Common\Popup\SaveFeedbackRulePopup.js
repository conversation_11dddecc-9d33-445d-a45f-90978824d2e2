/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
import Select from "react-select";
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const SaveFeedbackRulePopup = ({
  moduleslug = "",
  afterSaveData = () => {},
  feedbackRuleId = null,
  setFeedbackRuleId = () => {},
  setShowAlert = () => {},
  setAlertMessage = () => {},
  setMessageType = () => {},
}) => {
  const token = localStorage.getItem("token");
  const { t } = useTranslation();

  const params = useParams();

  const [senderRoleInput, setSenderRoleInput] = useState("");
  const [senderRoleOptions, setSenderRoleOptions] = useState([]);
  const [receiverRoleInput, setReceiverRoleInput] = useState("");
  const [receiverRoleOptions, setReceiverRoleOptions] = useState([]);
  const [surveyInput, setSurveyInput] = useState("");
  const [surveyOptions, setSurveyOptions] = useState([]);

  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [senderRoleValue, setSenderRoleValue] = useState(null);
  const [senderRoleId, setSenderRoleId] = useState(null);
  const [receiverRoleValue, setReceiverRoleValue] = useState(null);
  const [receiverRoleId, setReceiverRoleId] = useState(null);
  const [surveyValue, setSurveyValue] = useState(null);
  const [surveyId, setSurveyId] = useState(null);

  const [isSaving, setIsSaving] = useState(false);

  /* hook for validation */
  const [validation, setValidation] = useState({
    titleWarning: false,
    slugWarning: false,
    senderRoleWarning: false,
    receiverRoleWarning: false,
    surveyWarning: false,
  });

  /* function to validate form */
  const validate = () => {
    let isValid = true;

    if (title === "") {
      setValidation((prevState) => ({ ...prevState, titleWarning: true }));
      isValid = false;
    }

    if (slug === "") {
      setValidation((prevState) => ({ ...prevState, slugWarning: true }));
      isValid = false;
    }

    if (senderRoleId === null) {
      setValidation((prevState) => ({ ...prevState, senderRoleWarning: true }));
      isValid = false;
    }

    if (receiverRoleId === null) {
      setValidation((prevState) => ({
        ...prevState,
        receiverRoleWarning: true,
      }));
      isValid = false;
    }

    if (surveyId === null) {
      setValidation((prevState) => ({ ...prevState, surveyWarning: true }));
      isValid = false;
    }

    return isValid;
  };

  // API call for sender/receiver roles
  const getRoles = async (input, setOptions) => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_MODULE_ROLE + // Assuming this API can be filtered by name/slug
        `?token=${token}&slug=MOD_EVENT&filtername=${input}`;

      const response = await getData(requestUrl);
      if (response.status) {
        setOptions(response.data);
      }
    } catch (error) {
      console.error("Error fetching roles:", error.message);
    }
  };

  // API call for surveys
  const getSurveys = async (input) => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_SURVEY + // Assuming this API can be filtered by name
        `?token=${token}&moduleslug=MOD_EVENT&filtername=${input}`;

      const response = await getData(requestUrl);

      // console.log("response", response);

      if (response.status) {
        const apiData = response.data.map((item) => ({
          value: item._id,
          label: item.name,
        }));

        setSurveyOptions(apiData);
      }
    } catch (error) {
      console.error("Error fetching surveys:", error.message);
    }
  };

  // Handlers for select changes
  const senderRoleChangeHandler = (val) => {
    if (val) {
      setValidation((prevState) => ({
        ...prevState,
        senderRoleWarning: false,
      }));
      setSenderRoleValue(val);
      setSenderRoleId(val.value);
    } else {
      setSenderRoleValue(null);
      setSenderRoleId(null);
    }
  };

  const receiverRoleChangeHandler = (val) => {
    if (val) {
      setValidation((prevState) => ({
        ...prevState,
        receiverRoleWarning: false,
      }));
      setReceiverRoleValue(val);
      setReceiverRoleId(val.value);
    } else {
      setReceiverRoleValue(null);
      setReceiverRoleId(null);
    }
  };

  const surveyChangeHandler = (val) => {
    if (val) {
      setValidation((prevState) => ({ ...prevState, surveyWarning: false }));
      setSurveyValue(val);
      setSurveyId(val.value);
    } else {
      setSurveyValue(null);
      setSurveyId(null);
    }
  };

  // Get feedback rule details for editing
  const getFeedbackRuleDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_FEEDBACK_RULE_DETAILS + // Placeholder API endpoint
        `/${feedbackRuleId}?token=${token}`;

      const response = await getData(requestUrl);

      console.log("response", response);

      if (response.status) {
        setTitle(response.data.title);
        setSlug(response.data.slug);
        if (response.data.senderrole) {
          senderRoleChangeHandler({
            value: response.data.senderrole._id,
            label: response.data.senderrole.name,
          });
        }
        if (response.data.recieverrole) {
          receiverRoleChangeHandler({
            value: response.data.recieverrole._id,
            label: response.data.recieverrole.name,
          });
        }
        if (response.data.surveyid) {
          surveyChangeHandler({
            value: response.data.surveyid._id,
            label: response.data.surveyid.name,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching feedback rule details:", error.message);
    }
  };

  // Save handler
  const saveHandler = async () => {
    if (validate()) {
      setIsSaving(true);
      try {
        let sendingData = {
          title,
          slug,
          senderrole: senderRoleId,
          recieverrole: receiverRoleId,
          surveyid: surveyId,
          eventid: params.id,
        };

        let requestUrl = url.API_BASE_URL;
        let response = {};

        if (feedbackRuleId) {
          requestUrl =
            requestUrl +
            url.API_UPDATE_FEEDBACK_RULE + // Placeholder API endpoint
            `/${feedbackRuleId}?token=${token}`;
          response = await putData(requestUrl, sendingData);
        } else {
          requestUrl =
            requestUrl + url.API_CREATE_FEEDBACK_RULE + `?token=${token}`; // Placeholder API endpoint
          response = await postData(requestUrl, sendingData);
        }

        setIsSaving(false);

        if (response.status) {
          bootstrap.Offcanvas.getInstance(
            document.querySelector("#savefeedbackrule")
          ).hide();
          resetHandler();
          afterSaveData();
        } else {
          console.error("Save failed:", response.message);
        }
      } catch (error) {
        setIsSaving(false);
        console.error("Error saving feedback rule:", error.message);
      }
    }
  };

  /* reset */
  const resetHandler = () => {
    setTitle("");
    setSenderRoleValue(null);
    setSenderRoleId(null);
    setReceiverRoleValue(null);
    setReceiverRoleId(null);
    setSurveyValue(null);
    setSurveyId(null);
    setFeedbackRuleId(null);
    setValidation({
      titleWarning: false,
      slugWarning: false,
      senderRoleWarning: false,
      receiverRoleWarning: false,
      surveyWarning: false,
    });
  };

  useEffect(() => {
    if (feedbackRuleId) {
      getFeedbackRuleDetails();
    }
  }, [feedbackRuleId]);

  useEffect(() => {
    if (senderRoleInput.length > 0) {
      getRoles(senderRoleInput, setSenderRoleOptions);
    } else {
      setSenderRoleOptions([]);
    }
  }, [senderRoleInput]);

  useEffect(() => {
    if (receiverRoleInput.length > 0) {
      getRoles(receiverRoleInput, setReceiverRoleOptions);
    } else {
      setReceiverRoleOptions([]);
    }
  }, [receiverRoleInput]);

  useEffect(() => {
    if (surveyInput.length > 0) {
      getSurveys(surveyInput);
    } else {
      setSurveyOptions([]);
    }
  }, [surveyInput]);

  return (
    <div
      className="offcanvas lg offcanvas-end bg-white border-0"
      tabIndex="-1"
      id="savefeedbackrule"
      aria-labelledby="savefeedbackrule"
    >
      <div className="offcanvas-header p-4 pb-0">
        <h3 className="offcanvas-title" id="offcanvasLabelDetailsLabel">
          {t("Save Feedback Rule")}
        </h3>
        <button
          type="button"
          className="btn-close p-0 bg-gray-200 rounded-circle shadow-none m-0"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
          onClick={resetHandler}
        ></button>
      </div>
      <div className="offcanvas-body p-4">
        <form
          onSubmit={(e) => e.preventDefault()}
          className="d-flex flex-column h-100"
        >
          <div className="fields-container flex-fill">
            {/* Title */}
            <div className="form-group mb-4">
              <label
                htmlFor="ruleTitle"
                className="d-block fs-sm fw-semibold mb-2"
              >
                {t("Title")}
              </label>
              <input
                type="text"
                id="ruleTitle"
                className="form-control fs-sm shadow-none"
                placeholder={t("Enter here")}
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  setValidation((prevState) => ({
                    ...prevState,
                    titleWarning: false,
                  }));
                }}
              />
              {validation.titleWarning && (
                <div className="error-message mt-2">
                  <p className="d-flex align-items-center gap-1 text-danger">
                    <span className="material-symbols-outlined">warning</span>
                    <span>{t("Please enter title")}!</span>
                  </p>
                </div>
              )}
            </div>

            {/* slug  */}
            <div className="form-group mb-4">
              <label
                htmlFor="ruleSlug"
                className="d-block fs-sm fw-semibold mb-2"
              >
                {t("Slug")}
              </label>
              <input
                type="text"
                id="ruleSlug"
                className="form-control fs-sm shadow-none"
                placeholder={t("Enter here")}
                value={slug}
                onChange={(e) => {
                  setSlug(e.target.value);
                  setValidation((prevState) => ({
                    ...prevState,
                    slugWarning: false,
                  }));
                }}
              />
              {validation.slugWarning && (
                <div className="error-message mt-2">
                  <p className="d-flex align-items-center gap-1 text-danger">
                    <span className="material-symbols-outlined">warning</span>
                    <span>{t("Please enter slug")}!</span>
                  </p>
                </div>
              )}
            </div>

            {/* Sender Role */}
            <div className="form-group mb-4">
              <label
                htmlFor="senderRole"
                className="d-block fs-sm fw-semibold mb-2"
              >
                {t("Sender Role")}
              </label>
              <Select
                placeholder={t("Select Sender Role")}
                isClearable
                options={senderRoleOptions}
                value={senderRoleValue}
                onChange={(val) => senderRoleChangeHandler(val)}
                onInputChange={(val) => setSenderRoleInput(val)}
              />
              {validation.senderRoleWarning && (
                <div className="error-message mt-2">
                  <p className="d-flex align-items-center gap-1 text-danger">
                    <span className="material-symbols-outlined">warning</span>
                    <span>{t("Please select sender role")}!</span>
                  </p>
                </div>
              )}
            </div>

            {/* Receiver Role */}
            <div className="form-group mb-4">
              <label
                htmlFor="receiverRole"
                className="d-block fs-sm fw-semibold mb-2"
              >
                {t("Receiver Role")}
              </label>
              <Select
                placeholder={t("Select Receiver Role")}
                isClearable
                options={receiverRoleOptions}
                value={receiverRoleValue}
                onChange={(val) => receiverRoleChangeHandler(val)}
                onInputChange={(val) => setReceiverRoleInput(val)}
              />
              {validation.receiverRoleWarning && (
                <div className="error-message mt-2">
                  <p className="d-flex align-items-center gap-1 text-danger">
                    <span className="material-symbols-outlined">warning</span>
                    <span>{t("Please select receiver role")}!</span>
                  </p>
                </div>
              )}
            </div>

            {/* Survey */}
            <div className="form-group mb-4">
              <label
                htmlFor="survey"
                className="d-block fs-sm fw-semibold mb-2"
              >
                {t("Survey")}
              </label>
              <Select
                placeholder={t("Select Survey")}
                isClearable
                options={surveyOptions}
                value={surveyValue}
                onChange={(val) => surveyChangeHandler(val)}
                onInputChange={(val) => setSurveyInput(val)}
              />
              {validation.surveyWarning && (
                <div className="error-message mt-2">
                  <p className="d-flex align-items-center gap-1 text-danger">
                    <span className="material-symbols-outlined">warning</span>
                    <span>{t("Please select survey")}!</span>
                  </p>
                </div>
              )}
            </div>
          </div>
          <div className="action d-flex justify-content-between gap-3">
            <Link
              to="#"
              className="btn btn-outline-primary"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
              onClick={resetHandler}
            >
              {t("Close")}
            </Link>
            <button
              onClick={saveHandler}
              type="button"
              className="btn btn-primary"
              disabled={isSaving ? true : false}
              style={{
                cursor: isSaving ? "not-allowed" : "pointer",
              }}
            >
              {t("Save")}
              {isSaving && (
                <div
                  className="mx-2 spinner-border spinner-border-sm"
                  role="status"
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SaveFeedbackRulePopup;
